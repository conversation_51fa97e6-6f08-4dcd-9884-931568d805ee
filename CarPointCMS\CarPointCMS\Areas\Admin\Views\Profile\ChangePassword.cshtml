@{
    ViewData["Title"] = "Change Password";
}

@model CarPointCMS.Models.ViewModels.AdminChangePasswordViewModel

<h1 class="h3 mb-3 text-gray-800">Change Password</h1>

@if (TempData["success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["success"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@if (TempData["error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["error"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

<div class="row">
    <div class="col-md-6">
        <form asp-area="Admin" asp-controller="Profile" asp-action="ChangePassword" method="post">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-lock"></i> Password Security
                    </h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label asp-for="CurrentPassword" class="form-label">Current Password *</label>
                        <input asp-for="CurrentPassword" type="password" class="form-control" placeholder="Enter your current password" autofocus>
                        <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="NewPassword" class="form-label">New Password *</label>
                        <input asp-for="NewPassword" type="password" class="form-control" placeholder="Enter new password">
                        <span asp-validation-for="NewPassword" class="text-danger"></span>
                        <small class="form-text text-muted">Password must be at least 6 characters long</small>
                    </div>

                    <div class="form-group">
                        <label asp-for="ConfirmPassword" class="form-label">Confirm New Password *</label>
                        <input asp-for="ConfirmPassword" type="password" class="form-control" placeholder="Confirm new password">
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-0">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Update Password
                        </button>
                        <a href="@Url.Action("Change", "Profile", new { area = "Admin" })" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Profile
                        </a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Password Security Tips -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-shield-alt"></i> Password Security Tips
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        Use at least 6 characters (longer is better)
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        Include uppercase and lowercase letters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        Add numbers and special characters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        Avoid common words or personal information
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success"></i>
                        Don't reuse passwords from other accounts
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Add password strength indicator
            $('#NewPassword').on('input', function() {
                var password = $(this).val();
                var strength = getPasswordStrength(password);
                updatePasswordStrengthIndicator(strength);
            });

            function getPasswordStrength(password) {
                var score = 0;
                if (password.length >= 6) score++;
                if (password.length >= 8) score++;
                if (/[a-z]/.test(password)) score++;
                if (/[A-Z]/.test(password)) score++;
                if (/[0-9]/.test(password)) score++;
                if (/[^A-Za-z0-9]/.test(password)) score++;

                if (score < 3) return 'weak';
                if (score < 5) return 'medium';
                return 'strong';
            }

            function updatePasswordStrengthIndicator(strength) {
                var indicator = $('#password-strength');
                if (indicator.length === 0) {
                    $('#NewPassword').after('<div id="password-strength" class="mt-1"></div>');
                    indicator = $('#password-strength');
                }

                var html = '<small class="form-text">Password strength: ';
                if (strength === 'weak') {
                    html += '<span class="text-danger">Weak</span>';
                } else if (strength === 'medium') {
                    html += '<span class="text-warning">Medium</span>';
                } else {
                    html += '<span class="text-success">Strong</span>';
                }
                html += '</small>';

                indicator.html(html);
            }
        });
    </script>
}
