using Microsoft.AspNetCore.Mvc;
using CarPointCMS.Data;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Models.Entities;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using CarPointCMS.Common;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace CarPointCMS.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class AuthController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public AuthController(ApplicationDbContext context, IAuthenticationService authService)
            : base(authService)
        {
            _context = context;
        }

        [HttpGet]
        public IActionResult Login()
        {
            // If already logged in as admin, redirect to dashboard
            if (IsAuthenticated && IsAdmin)
            {
                return RedirectToAction("Index", "Home");
            }

            // If logged in as customer, redirect to customer area
            if (IsAuthenticated && IsCustomer)
            {
                return RedirectToAction("Index", "Home", new { area = "Customer" });
            }

            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Login(AdminLoginViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                // Find admin by email
                var admin = await _context.Admins
                    .FirstOrDefaultAsync(a => a.Email == model.Email);

                if (admin == null)
                {
                    ModelState.AddModelError("", "Invalid email or password.");
                    return View(model);
                }

                // Check if admin is active
                if (admin.Status != UserStatus.Active || !admin.IsActive)
                {
                    ModelState.AddModelError("", "Your account is not active. Please contact administrator.");
                    return View(model);
                }

                // Verify password using SHA256 with salt
                if (!VerifyPassword(model.Password, admin.Password))
                {
                    ModelState.AddModelError("", "Invalid email or password.");
                    return View(model);
                }

                // Sign in using the authentication service
                var signInResult = await SignInAdminAsync(admin);
                if (!signInResult)
                {
                    ModelState.AddModelError("", "An error occurred during login. Please try again.");
                    return View(model);
                }

                SetSuccessMessage("Login successful!");
                return RedirectToAction("Index", "Home");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred during login. Please try again.");
                return View(model);
            }
        }

        [HttpPost]
        public async Task<IActionResult> Logout()
        {
            await SignOutAsync();
            SetSuccessMessage("You have been logged out successfully.");
            return RedirectToAction("Login");
        }

        [HttpGet]
        public IActionResult ForgotPassword()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> ForgotPassword(AdminForgotPasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var admin = await _context.Admins
                    .FirstOrDefaultAsync(a => a.Email == model.Email);

                if (admin == null)
                {
                    ModelState.AddModelError("", "Email address not found.");
                    return View(model);
                }

                // Generate reset token
                var token = GenerateResetToken();
                admin.Token = token;
                admin.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // TODO: Send email with reset link
                // For now, just show success message
                TempData["success"] = "Password reset link has been sent to your email.";
                return RedirectToAction("Login");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred. Please try again.");
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> ResetPassword(string token, string email)
        {
            if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(email))
            {
                return RedirectToAction("Login");
            }

            var admin = await _context.Admins
                .FirstOrDefaultAsync(a => a.Token == token && a.Email == email);

            if (admin == null)
            {
                return RedirectToAction("Login");
            }

            var model = new AdminResetPasswordViewModel
            {
                Token = token,
                Email = email
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> ResetPassword(AdminResetPasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var admin = await _context.Admins
                    .FirstOrDefaultAsync(a => a.Token == model.Token && a.Email == model.Email);

                if (admin == null)
                {
                    return RedirectToAction("Login");
                }

                // Update password using SHA256 with salt
                admin.Password = HashPassword(model.NewPassword);
                admin.Token = null; // Clear token
                admin.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                TempData["success"] = "Password has been reset successfully.";
                return RedirectToAction("Login");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred. Please try again.");
                return View(model);
            }
        }

        private string GenerateResetToken()
        {
            using (var sha256 = SHA256.Create())
            {
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(timestamp));
                return Convert.ToHexString(hash).ToLower();
            }
        }

        // Helper methods for password hashing
        private string HashPassword(string password)
        {
            const string salt = "CarPointCMS_Salt_2024";
            using var sha256 = SHA256.Create();
            var saltedPassword = password + salt;
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
            return Convert.ToBase64String(hashedBytes);
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {
            var hashOfInput = HashPassword(password);
            return hashOfInput.Equals(hashedPassword);
        }
    }
}
