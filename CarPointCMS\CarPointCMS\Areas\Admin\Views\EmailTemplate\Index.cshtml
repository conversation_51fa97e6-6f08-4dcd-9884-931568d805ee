@using CarPointCMS.Models.Entities
@{
    ViewData["Title"] = "Email Template";
}

@model IEnumerable<EmailTemplate>

<h1 class="h3 mb-3 text-gray-800">Email Template</h1>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="" width="100%" cellspacing="0">
                <thead>
                <tr>
                    <th>Serial</th>
                    <th>Name</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                    @if (Model != null)
                    {
                        int i = 1;
                        @foreach (var row in Model)
                        {
                            <tr>
                                <td>@i</td>
                                <td>@row.EtName</td>
                                <td>
                                    <a asp-area="Admin" asp-controller="EmailTemplate" asp-action="Edit" asp-route-id="@row.Id" class="btn btn-warning btn-sm"><i class="fas fa-edit"></i></a>
                                </td>
                            </tr>
                            i++;
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
