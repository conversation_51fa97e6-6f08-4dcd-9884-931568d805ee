using CarPointCMS.Models.Entities;
using System.ComponentModel.DataAnnotations;

namespace CarPointCMS.Models.ViewModels
{
    public class CustomerEditPasswordViewModel
    {
        public PageOtherItem? PageOtherItem { get; set; }

        [Required(ErrorMessage = "New password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be at least 6 characters long")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [Required(ErrorMessage = "Confirm password is required")]
        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match")]
        public string RePassword { get; set; } = "";
    }
}
