@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Add Amenity";
}

@model AmenityCreateViewModel

<h1 class="h3 mb-3 text-gray-800">Add Amenity</h1>

<form asp-area="Admin" asp-controller="Amenity" asp-action="Create" method="post">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary">Add Amenity</h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="Amenity" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="AmenityName">Name *</label>
                <input asp-for="AmenityName" class="form-control" autofocus>
                <span asp-validation-for="AmenityName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="AmenitySlug">Slug</label>
                <input asp-for="AmenitySlug" class="form-control">
                <span asp-validation-for="AmenitySlug" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-success">Submit</button>
        </div>
    </div>
</form>
