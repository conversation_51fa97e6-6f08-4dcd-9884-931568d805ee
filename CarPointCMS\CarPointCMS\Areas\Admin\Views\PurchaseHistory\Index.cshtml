@using CarPointCMS.Models.Entities
@{
    ViewData["Title"] = "Purchase History";
}

@model IEnumerable<PackagePurchase>

<h1 class="h3 mb-3 text-gray-800">Purchase History</h1>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                <tr>
                    <th>Serial</th>
                    <th>Customer Name</th>
                    <th>Package Name</th>
                    <th>Package Start Date</th>
                    <th>Package End Date</th>
                    <th>Paid Amount</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                    @if (Model != null)
                    {
                        int i = 1;
                        @foreach (var row in Model)
                        {
                            <tr>
                                <td>@i</td>
                                <td>
                                    @row.User?.Name
                                    <br><a asp-area="Admin" asp-controller="Customer" asp-action="Detail" asp-route-id="@row.UserId" class="badge badge-success" target="_blank">See Detail</a>
                                </td>
                                <td>
                                    @row.Package?.PackageName
                                    @if (row.CurrentlyActive)
                                    {
                                        <br><span class="badge badge-primary">Currently Active</span>
                                    }
                                </td>
                                <td>
                                    @row.PackageStartDate.ToString("dd MMMM, yyyy")
                                </td>
                                <td>
                                    @row.PackageEndDate.ToString("dd MMMM, yyyy")
                                </td>
                                <td>@<EMAIL>("N2")</td>
                                <td>
                                    <a asp-area="Admin" asp-controller="PurchaseHistory" asp-action="Invoice" asp-route-id="@row.Id" class="btn btn-warning btn-sm btn-block mb_5">Invoice</a>
                                    <a asp-area="Admin" asp-controller="PurchaseHistory" asp-action="Detail" asp-route-id="@row.Id" class="btn btn-secondary btn-sm btn-block mb_5">History Detail</a>
                                    <a href="@Url.Action("Pricing", "Home", new { area = "" })" class="btn btn-primary btn-sm btn-block" target="_blank">Package Detail</a>
                                </td>
                            </tr>
                            i++;
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
