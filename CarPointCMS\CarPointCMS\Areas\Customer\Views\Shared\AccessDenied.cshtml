@{
    ViewData["Title"] = "Access Denied";
    Layout = "_Layout";
}

<div class="page-banner" style="background-image: url('~/uploads/page_banners/default_banner.jpg')">
    <div class="bg-page"></div>
    <div class="text">
        <h1>Access Denied</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Access Denied</li>
            </ol>
        </nav>
    </div>
</div>

<div class="page-content">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="access-denied-wrapper">
                    
                    <!-- Error Icon and Code -->
                    <div class="text-center mb-5">
                        <div class="error-icon-wrapper">
                            <div class="error-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="error-code">403</div>
                        </div>
                        <h2 class="error-title">Access Denied</h2>
                        <p class="error-subtitle">You don't have permission to access this resource</p>
                    </div>
                    
                    <!-- Error Message -->
                    <div class="error-message-box">
                        <div class="row">
                            <div class="col-md-2 text-center">
                                <i class="fas fa-exclamation-triangle text-warning fa-3x"></i>
                            </div>
                            <div class="col-md-10">
                                <h5>Why am I seeing this page?</h5>
                                <p class="mb-0">
                                    This area requires special permissions or authentication. 
                                    You may need to log in with appropriate credentials or contact 
                                    the administrator for access.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <a href="javascript:history.back()" class="btn btn-outline-secondary btn-block btn-lg">
                                    <i class="fas fa-arrow-left"></i>
                                    Go Back
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="@Url.Action("Index", "Home")" class="btn btn-primary btn-block btn-lg">
                                    <i class="fas fa-home"></i>
                                    Home Page
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="@Url.Action("Login", "Account")" class="btn btn-success btn-block btn-lg">
                                    <i class="fas fa-sign-in-alt"></i>
                                    Login
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Help Section -->
                    <div class="help-section">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-question-circle text-info"></i>
                                    Need Help?
                                </h6>
                                <p class="card-text">
                                    If you believe you should have access to this resource, please:
                                </p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Verify you are logged in with the correct account</li>
                                    <li><i class="fas fa-check text-success"></i> Check if your account has the required permissions</li>
                                    <li><i class="fas fa-check text-success"></i> Contact our support team if the issue persists</li>
                                </ul>
                                <a href="@Url.Action("Contact", "Home")" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-envelope"></i>
                                    Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .access-denied-wrapper {
        padding: 60px 0;
    }
    
    .error-icon-wrapper {
        position: relative;
        display: inline-block;
        margin-bottom: 30px;
    }
    
    .error-icon {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        animation: pulse 2s infinite;
        box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
    }
    
    .error-icon i {
        font-size: 50px;
        color: white;
    }
    
    .error-code {
        position: absolute;
        top: -10px;
        right: -10px;
        background: #f39c12;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
        box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
    }
    
    .error-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .error-subtitle {
        font-size: 1.2rem;
        color: #7f8c8d;
        margin-bottom: 0;
    }
    
    .error-message-box {
        background: #f8f9fa;
        border-left: 4px solid #e74c3c;
        padding: 30px;
        margin: 40px 0;
        border-radius: 0 10px 10px 0;
    }
    
    .action-buttons {
        margin: 40px 0;
    }
    
    .help-section {
        margin-top: 50px;
    }
    
    .btn {
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }
    
    @keyframes pulse {
        0% {
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
        }
        50% {
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.6);
        }
        100% {
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
        }
    }
    
    @media (max-width: 768px) {
        .error-title {
            font-size: 2rem;
        }
        
        .error-message-box {
            padding: 20px;
        }
        
        .error-message-box .row {
            text-align: center;
        }
        
        .error-message-box .col-md-2 {
            margin-bottom: 20px;
        }
    }
</style>
