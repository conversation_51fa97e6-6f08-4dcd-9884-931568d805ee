using Microsoft.AspNetCore.Mvc;
using CarPointCMS.Data;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Models.Entities;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using CarPointCMS.Attributes;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace CarPointCMS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminOnly]
    public class ProfileController : BaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public ProfileController(ApplicationDbContext context, IWebHostEnvironment webHostEnvironment, IAuthenticationService authService)
            : base(authService)
        {
            _context = context;
            _webHostEnvironment = webHostEnvironment;
        }

        [HttpGet]
        public async Task<IActionResult> Change()
        {
            var admin = await GetCurrentAdminAsync();
            if (admin == null)
                return RedirectToAdminLogin();

            var model = new AdminProfileChangeViewModel
            {
                Name = admin.Name,
                Email = admin.Email,
                Phone = admin.Phone,
                Country = admin.Country,
                Address = admin.Address,
                State = admin.State,
                City = admin.City,
                Zip = admin.Zip,
                Website = admin.Website,
                Facebook = admin.Facebook,
                Twitter = admin.Twitter,
                LinkedIn = admin.LinkedIn,
                Instagram = admin.Instagram,
                Pinterest = admin.Pinterest,
                Youtube = admin.Youtube,
                ExistingPhoto = admin.Photo,
                ExistingBanner = admin.Banner
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Change(AdminProfileChangeViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var admin = await GetCurrentAdminAsync();
                if (admin == null)
                    return RedirectToAdminLogin();

                // Check if email is already taken by another admin
                var existingAdmin = await _context.Admins
                    .FirstOrDefaultAsync(a => a.Email == model.Email && a.Id != admin.Id);

                if (existingAdmin != null)
                {
                    ModelState.AddModelError("Email", "Email address is already taken.");
                    return View(model);
                }

                // Handle photo upload
                if (model.Photo != null && model.Photo.Length > 0)
                {
                    // Delete old photo if exists
                    if (!string.IsNullOrEmpty(admin.Photo))
                    {
                        DeleteFile(admin.Photo, "admin");
                    }

                    admin.Photo = await SaveUploadedFileAsync(model.Photo, "admin");
                }

                // Handle banner upload
                if (model.Banner != null && model.Banner.Length > 0)
                {
                    // Delete old banner if exists
                    if (!string.IsNullOrEmpty(admin.Banner))
                    {
                        DeleteFile(admin.Banner, "admin");
                    }

                    admin.Banner = await SaveUploadedFileAsync(model.Banner, "admin");
                }

                // Update admin properties
                admin.Name = model.Name;
                admin.Email = model.Email;
                admin.Phone = model.Phone;
                admin.Country = model.Country;
                admin.Address = model.Address;
                admin.State = model.State;
                admin.City = model.City;
                admin.Zip = model.Zip;
                admin.Website = model.Website;
                admin.Facebook = model.Facebook;
                admin.Twitter = model.Twitter;
                admin.LinkedIn = model.LinkedIn;
                admin.Instagram = model.Instagram;
                admin.Pinterest = model.Pinterest;
                admin.Youtube = model.Youtube;
                admin.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Update session data
                HttpContext.Session.SetString("AdminName", admin.Name ?? "Admin");
                HttpContext.Session.SetString("AdminEmail", admin.Email);

                SetSuccessMessage("Profile has been updated successfully.");
                return RedirectToAction("Change");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while updating the profile.");
                return View(model);
            }
        }

        [HttpGet]
        public IActionResult ChangePassword()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> ChangePassword(AdminChangePasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var admin = await GetCurrentAdminAsync();
                if (admin == null)
                    return RedirectToAdminLogin();

                // Verify current password using SHA256 with salt
                if (!VerifyPassword(model.CurrentPassword, admin.Password))
                {
                    ModelState.AddModelError("CurrentPassword", "Current password is incorrect.");
                    return View(model);
                }

                // Update password using SHA256 with salt
                admin.Password = HashPassword(model.NewPassword);
                admin.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                SetSuccessMessage("Password has been changed successfully.");
                return RedirectToAction("ChangePassword");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while changing the password.");
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> PhotoChange()
        {
            var admin = await GetCurrentAdminAsync();
            if (admin == null)
                return RedirectToAdminLogin();

            ViewBag.CurrentUser = admin;
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> PhotoChange(AdminPhotoChangeViewModel model)
        {
            var admin = await GetCurrentAdminAsync();
            if (admin == null)
                return RedirectToAdminLogin();

            ViewBag.CurrentUser = admin;

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(model.Photo.FileName).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    ModelState.AddModelError("Photo", "Please upload a valid image file (JPG, PNG, GIF).");
                    return View(model);
                }

                // Validate file size (max 2MB)
                if (model.Photo.Length > 2 * 1024 * 1024)
                {
                    ModelState.AddModelError("Photo", "File size cannot exceed 2MB.");
                    return View(model);
                }

                // Delete old photo if exists
                if (!string.IsNullOrEmpty(admin.Photo))
                {
                    DeleteFile(admin.Photo, "admin");
                }

                // Save new photo
                admin.Photo = await SaveUploadedFileAsync(model.Photo, "admin");
                admin.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                SetSuccessMessage("Photo has been updated successfully.");
                return RedirectToActionInArea("PhotoChange");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while updating the photo.");
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> BannerChange()
        {
            var admin = await GetCurrentAdminAsync();
            if (admin == null)
                return RedirectToAdminLogin();

            ViewBag.AdminData = admin;
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> BannerChange(AdminBannerChangeViewModel model)
        {
            var admin = await GetCurrentAdminAsync();
            if (admin == null)
                return RedirectToAdminLogin();

            ViewBag.AdminData = admin;

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(model.Banner.FileName).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    ModelState.AddModelError("Banner", "Please upload a valid image file (JPG, PNG, GIF).");
                    return View(model);
                }

                // Validate file size (max 5MB for banners)
                if (model.Banner.Length > 5 * 1024 * 1024)
                {
                    ModelState.AddModelError("Banner", "File size cannot exceed 5MB.");
                    return View(model);
                }

                // Delete old banner if exists
                if (!string.IsNullOrEmpty(admin.Banner))
                {
                    DeleteFile(admin.Banner, "admin");
                }

                // Save new banner
                admin.Banner = await SaveUploadedFileAsync(model.Banner, "admin");
                admin.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                SetSuccessMessage("Banner has been updated successfully.");
                return RedirectToActionInArea("BannerChange");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while updating the banner.");
                return View(model);
            }
        }

        // Helper methods for password hashing
        private string HashPassword(string password)
        {
            const string salt = "CarPointCMS_Salt_2024";
            using var sha256 = SHA256.Create();
            var saltedPassword = password + salt;
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
            return Convert.ToBase64String(hashedBytes);
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {
            var hashOfInput = HashPassword(password);
            return hashOfInput.Equals(hashedPassword);
        }
    }
}
