@using CarPointCMS.Models.ViewModels
@using CarPointCMS.Models.Entities
@model AdminDashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="row">
    <div class="col-xl-12 col-md-12 mb-2">
        <h1 class="h3 mb-3 text-gray-800">Dashboard</h1>
    </div>
</div>

<!-- Box Start -->
<div class="row dashboard-page">

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1">Active Customers</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Stats.TotalActiveCustomers</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-friends fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1">Pending Customers</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Stats.TotalPendingCustomers</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-friends fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1">Active Listings</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Stats.TotalActiveListings</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1">Pending Listings</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Stats.TotalPendingListings</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cart-arrow-down fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- // Box End -->

<!-- Recent Data Section -->
<div class="row mt-4">

    <!-- Recent Listings -->
    <div class="col-xl-4 col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent Listings</h6>
                <a href="@Url.Action("Index", "Listing")" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                @if (Model.RecentListings.Any())
                {
                    @foreach (var listing in Model.RecentListings)
                    {
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                @if (!string.IsNullOrEmpty(listing.ListingFeaturedPhoto))
                                {
                                    <img src="~/uploads/listing_featured_photos/@listing.ListingFeaturedPhoto"
                                         alt="@listing.ListingName"
                                         class="rounded"
                                         style="width: 50px; height: 50px; object-fit: cover;">
                                }
                                else
                                {
                                    <div class="bg-secondary rounded d-flex align-items-center justify-content-center"
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-car text-white"></i>
                                    </div>
                                }
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold text-truncate" style="max-width: 200px;">
                                    @listing.ListingName
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-user"></i> @listing.User?.Name
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-calendar"></i> @listing.CreatedAt.ToString("MMM dd, yyyy")
                                </div>
                                <div class="small">
                                    @if (listing.ListingStatus == ListingStatus.Active)
                                    {
                                        <span class="badge badge-success">Active</span>
                                    }
                                    else if (listing.ListingStatus == ListingStatus.Pending)
                                    {
                                        <span class="badge badge-warning">Pending</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-danger">Inactive</span>
                                    }
                                </div>
                            </div>
                            <div class="ml-2">
                                <a href="@Url.Action("Details", "Listing", new { id = listing.Id })"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-car fa-2x mb-2"></i>
                        <p>No recent listings found</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Recent Users -->
    <div class="col-xl-4 col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent Users</h6>
                <a href="@Url.Action("Index", "Customer")" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                @if (Model.RecentUsers.Any())
                {
                    @foreach (var user in Model.RecentUsers)
                    {
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                @if (!string.IsNullOrEmpty(user.Photo))
                                {
                                    <img src="~/uploads/user_photos/@user.Photo"
                                         alt="@user.Name"
                                         class="rounded-circle"
                                         style="width: 50px; height: 50px; object-fit: cover;">
                                }
                                else
                                {
                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                }
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">@user.Name</div>
                                <div class="text-muted small">
                                    <i class="fas fa-envelope"></i> @user.Email
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-calendar"></i> @user.CreatedAt.ToString("MMM dd, yyyy")
                                </div>
                                <div class="small">
                                    @if (user.Status == UserStatus.Active)
                                    {
                                        <span class="badge badge-success">Active</span>
                                    }
                                    else if (user.Status == UserStatus.Pending)
                                    {
                                        <span class="badge badge-warning">Pending</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-danger">Inactive</span>
                                    }
                                </div>
                            </div>
                            <div class="ml-2">
                                <a href="@Url.Action("Details", "Customer", new { id = user.Id })"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <p>No recent users found</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Recent Purchases -->
    <div class="col-xl-4 col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent Purchases</h6>
                <a href="@Url.Action("Index", "PurchaseHistory")" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                @if (Model.RecentPurchases.Any())
                {
                    @foreach (var purchase in Model.RecentPurchases)
                    {
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-credit-card text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">@purchase.Package?.PackageName</div>
                                <div class="text-muted small">
                                    <i class="fas fa-user"></i> @purchase.User?.Name
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-dollar-sign"></i> @<EMAIL>("F2")
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-calendar"></i> @purchase.CreatedAt.ToString("MMM dd, yyyy")
                                </div>
                                <div class="small">
                                    @if (purchase.PaymentStatus == "Completed")
                                    {
                                        <span class="badge badge-success">Completed</span>
                                    }
                                    else if (purchase.PaymentStatus == "Pending")
                                    {
                                        <span class="badge badge-warning">Pending</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-danger">Failed</span>
                                    }

                                    @if (purchase.CurrentlyActive)
                                    {
                                        <span class="badge badge-primary ml-1">Active</span>
                                    }
                                </div>
                            </div>
                            <div class="ml-2">
                                <a href="@Url.Action("Detail", "PurchaseHistory", new { id = purchase.Id })"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <p>No recent purchases found</p>
                    </div>
                }
            </div>
        </div>
    </div>

</div>
<!-- // Recent Data Section End -->
