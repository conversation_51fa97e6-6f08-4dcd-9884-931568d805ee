@using CarPointCMS.Models.Entities
@{
    ViewData["Title"] = "Customers";
}

@model IEnumerable<User>

<h1 class="h3 mb-3 text-gray-800">Customers</h1>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                <tr>
                    <th>Serial</th>
                    <th>Photo</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Status</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                @if (Model != null)
                {
                    int i = 1;
                    @foreach (var row in Model)
                    {
                        <tr>
                            <td>@i</td>
                            <td>
                                @if (string.IsNullOrEmpty(row.Photo))
                                {
                                    <img src="~/uploads/user_photos/default_photo.jpg" class="w_100">
                                }
                                else
                                {
                                    <img src="~/uploads/user_photos/@row.Photo" class="w_100">
                                }
                            </td>
                            <td>@row.Name</td>
                            <td>@row.Email</td>
                            <td>
                                @if (row.Status == "Active")
                                {
                                    <a href="javascript:void(0)" onclick="customerStatus(@row.Id)"><input type="checkbox" checked data-toggle="toggle" data-on="Active" data-off="Pending" data-onstyle="success" data-offstyle="danger"></a>
                                }
                                else
                                {
                                    <a href="javascript:void(0)" onclick="customerStatus(@row.Id)"><input type="checkbox" data-toggle="toggle" data-on="Active" data-off="Pending" data-onstyle="success" data-offstyle="danger"></a>
                                }
                            </td>
                            <td>
                                <a asp-area="Admin" asp-controller="Customer" asp-action="Detail" asp-route-id="@row.Id" class="btn btn-info btn-sm btn-block">Detail</a>
                                <a asp-area="Admin" asp-controller="Customer" asp-action="Delete" asp-route-id="@row.Id" class="btn btn-danger btn-sm btn-block" onclick="return confirm('Are you sure?');">Delete</a>
                            </td>
                        </tr>
                        i++;
                    }
                }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function customerStatus(id){
            $.ajax({
                type:"get",
                url: "@Url.Action("ChangeStatus", "Customer", new { area = "Admin" })" + "/" + id,
                success:function(response){
                   toastr.success(response)
                },
                error:function(err){
                    console.log(err);
                }
            })
        }
    </script>
}
