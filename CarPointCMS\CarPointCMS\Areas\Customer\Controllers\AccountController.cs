using CarPointCMS.Data;
using CarPointCMS.Models.Entities;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using CarPointCMS.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace CarPointCMS.Areas.Customer.Controllers
{
    [Area("Customer")]
    public class AccountController : BaseController
    {
        private readonly ApplicationDbContext _db;
        private readonly ILogger<AccountController> _logger;

        public AccountController(ApplicationDbContext db, ILogger<AccountController> logger, IAuthenticationService authService)
            : base(authService)
        {
            _db = db;
            _logger = logger;
        }

        // GET: /Account/Login
        public async Task<IActionResult> Login()
        {
            // If already logged in as customer, redirect to customer dashboard
            if (IsAuthenticated && IsCustomer)
            {
                return RedirectToAction("Dashboard", "Customer");
            }

            // If logged in as admin, redirect to admin area
            if (IsAuthenticated && IsAdmin)
            {
                return RedirectToAction("Index", "Home", new { area = "Admin" });
            }

            var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
            var model = new LoginViewModel
            {
                PageOther = pageOther
            };
            return View(model);
        }

        // POST: /Account/Login
        [HttpPost]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            if (!ModelState.IsValid)
            {
                var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
                model.PageOther = pageOther;
                return View(model);
            }

            try
            {
                var user = await _db.Users
                    .FirstOrDefaultAsync(u => u.Email == model.Email && u.Status == UserStatus.Active);

                if (user != null && VerifyPassword(model.Password, user.Password))
                {
                    // Set session or authentication cookie here
                    HttpContext.Session.SetString("CustomerId", user.Id.ToString());
                    HttpContext.Session.SetString("CustomerName", user.Name);
                    HttpContext.Session.SetString("CustomerEmail", user.Email);

                    SetSuccessMessage("Login successful!");
                    return RedirectToAction("Dashboard", "Customer", new { Area = "Customer" });
                }
                else
                {
                    ModelState.AddModelError("", "Invalid email or password.");
                    var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
                    model.PageOther = pageOther;
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for email: {Email}", model.Email);
                ModelState.AddModelError("", "An error occurred during login. Please try again.");
                var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
                model.PageOther = pageOther;
                return View(model);
            }
        }

        // GET: /Account/Register
        public async Task<IActionResult> Register()
        {
            var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
            var model = new RegisterViewModel
            {
                PageOther = pageOther
            };
            return View(model);
        }

        // POST: /Account/Register
        [HttpPost]
        public async Task<IActionResult> Register(RegisterViewModel model)
        {
            if (!ModelState.IsValid)
            {
                var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
                model.PageOther = pageOther;
                return View(model);
            }

            try
            {
                // Check if email already exists
                var existingUser = await _db.Users.FirstOrDefaultAsync(u => u.Email == model.Email);
                if (existingUser != null)
                {
                    ModelState.AddModelError("Email", "Email address is already registered.");
                    var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
                    model.PageOther = pageOther;
                    return View(model);
                }

                // Create new user
                var user = new User
                {
                    Name = model.Name,
                    Email = model.Email,
                    Password = HashPassword(model.Password),
                    Status = UserStatus.Pending, // Requires email verification
                    Token = GenerateToken(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _db.Users.Add(user);
                await _db.SaveChangesAsync();

                // TODO: Send verification email here
                SetSuccessMessage("Registration successful! Please check your email for verification instructions.");
                return RedirectToAction(nameof(Login));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for email: {Email}", model.Email);
                ModelState.AddModelError("", "An error occurred during registration. Please try again.");
                var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
                model.PageOther = pageOther;
                return View(model);
            }
        }

        // GET: /Account/ForgotPassword
        public async Task<IActionResult> ForgotPassword()
        {
            var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
            var model = new ForgotPasswordViewModel
            {
                PageOtherItem = pageOther
            };
            return View(model);
        }

        // POST: /Account/ForgotPassword
        [HttpPost]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
                model.PageOtherItem = pageOther;
                return View(model);
            }

            try
            {
                var user = await _db.Users.FirstOrDefaultAsync(u => u.Email == model.Email);
                if (user != null)
                {
                    // Generate reset token
                    user.Token = GenerateToken();
                    user.UpdatedAt = DateTime.UtcNow;
                    await _db.SaveChangesAsync();

                    // TODO: Send password reset email here
                    SetSuccessMessage("Password reset instructions have been sent to your email.");
                }
                else
                {
                    // Don't reveal if email exists or not for security
                    SetSuccessMessage("If the email exists in our system, password reset instructions have been sent.");
                }

                return RedirectToAction("Login");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during forgot password for email: {Email}", model.Email);
                ModelState.AddModelError("", "An error occurred. Please try again.");
                var pageOther = await _db.PageOtherItems.FirstOrDefaultAsync();
                model.PageOtherItem = pageOther;
                return View(model);
            }
        }

        // GET: /Account/Logout
        public IActionResult Logout()
        {
            HttpContext.Session.Clear();
            SetSuccessMessage("You have been logged out successfully.");
            return RedirectToAction("Login");
        }

        // GET: /Account/VerifyEmail/{token}/{email}
        public async Task<IActionResult> VerifyEmail(string token, string email)
        {
            try
            {
                var user = await _db.Users.FirstOrDefaultAsync(u => u.Email == email && u.Token == token);
                if (user != null)
                {
                    user.Status = UserStatus.Active;
                    user.Token = null;
                    user.UpdatedAt = DateTime.UtcNow;
                    await _db.SaveChangesAsync();

                    SetSuccessMessage("Email verified successfully! You can now login.");
                }
                else
                {
                    SetErrorMessage("Invalid verification link.");
                }

                return RedirectToAction("Login");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during email verification for email: {Email}", email);
                SetErrorMessage("An error occurred during verification.");
                return RedirectToAction("Login");
            }
        }

        // Helper methods
        private string HashPassword(string password)
        {
            const string salt = "CarPointCMS_Salt_2024";
            using (var sha256 = SHA256.Create())
            {
                var saltedPassword = password + salt;
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {
            var hashOfInput = HashPassword(password);
            return hashOfInput == hashedPassword;
        }

        private string GenerateToken()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var bytes = new byte[32];
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes);
            }
        }
    }
}
