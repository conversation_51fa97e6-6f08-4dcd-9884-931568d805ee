@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Add Dynamic Page";
}

@model DynamicPageCreateViewModel

<h1 class="h3 mb-3 text-gray-800">Add Dynamic Page</h1>

<form asp-area="Admin" asp-controller="DynamicPage" asp-action="Create" method="post" enctype="multipart/form-data">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="DynamicPage" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="DynamicPageName">Name *</label>
                <input asp-for="DynamicPageName" class="form-control" autofocus>
                <span asp-validation-for="DynamicPageName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DynamicPageSlug">Slug</label>
                <input asp-for="DynamicPageSlug" class="form-control">
                <span asp-validation-for="DynamicPageSlug" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DynamicPageContent">Content</label>
                <textarea asp-for="DynamicPageContent" class="form-control editor" cols="30" rows="10"></textarea>
                <span asp-validation-for="DynamicPageContent" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DynamicPageBanner">Banner *</label>
                <div>
                    <input asp-for="DynamicPageBanner" type="file">
                    <span asp-validation-for="DynamicPageBanner" class="text-danger"></span>
                </div>
            </div>
        </div>
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="SeoTitle">Title</label>
                <input asp-for="SeoTitle" class="form-control">
                <span asp-validation-for="SeoTitle" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="SeoMetaDescription">Meta Description</label>
                <textarea asp-for="SeoMetaDescription" class="form-control h_100" cols="30" rows="10"></textarea>
                <span asp-validation-for="SeoMetaDescription" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-success">Submit</button>
        </div>
    </div>
</form>
