using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;
using CarPointCMS.Models.Entities;

namespace CarPointCMS.Models.ViewModels
{
    // Category ViewModels
    public class CategoryCreateViewModel
    {
        [Required(ErrorMessage = "Category name is required")]
        [StringLength(255, ErrorMessage = "Category name cannot exceed 255 characters")]
        public string CategoryName { get; set; } = "";

        [StringLength(255, ErrorMessage = "Category slug cannot exceed 255 characters")]
        public string? CategorySlug { get; set; }

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }
    }

    public class CategoryEditViewModel : CategoryCreateViewModel
    {
        public int Id { get; set; }
    }

    // Blog ViewModels
    public class BlogCreateViewModel
    {
        [Required(ErrorMessage = "Category is required")]
        public int CategoryId { get; set; }

        [Required(ErrorMessage = "Post title is required")]
        [StringLength(255, ErrorMessage = "Post title cannot exceed 255 characters")]
        public string PostTitle { get; set; } = "";

        [StringLength(255, ErrorMessage = "Post slug cannot exceed 255 characters")]
        public string? PostSlug { get; set; }

        [Required(ErrorMessage = "Post content is required")]
        public string PostContent { get; set; } = "";

        public string? PostContentShort { get; set; }

        public IFormFile? PostPhoto { get; set; }

        [Required(ErrorMessage = "Comment show option is required")]
        public string CommentShow { get; set; } = "Yes";

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public List<SelectListItem> Categories { get; set; } = new List<SelectListItem>();
    }

    public class BlogEditViewModel : BlogCreateViewModel
    {
        public int Id { get; set; }
        public string? ExistingPhoto { get; set; }
    }

    // FAQ ViewModels
    public class FaqCreateViewModel
    {
        [Required(ErrorMessage = "FAQ question is required")]
        [StringLength(255, ErrorMessage = "FAQ question cannot exceed 255 characters")]
        public string FaqQuestion { get; set; } = "";

        [Required(ErrorMessage = "FAQ answer is required")]
        public string FaqAnswer { get; set; } = "";
    }

    public class FaqEditViewModel : FaqCreateViewModel
    {
        public int Id { get; set; }
    }

    // Testimonial ViewModels
    public class TestimonialCreateViewModel
    {
        [Required(ErrorMessage = "Name is required")]
        [StringLength(255, ErrorMessage = "Name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        [StringLength(255, ErrorMessage = "Designation cannot exceed 255 characters")]
        public string? Designation { get; set; }

        [Required(ErrorMessage = "Comment is required")]
        public string Comment { get; set; } = "";

        public IFormFile? Photo { get; set; }
    }

    public class TestimonialEditViewModel : TestimonialCreateViewModel
    {
        public int Id { get; set; }
        public string? ExistingPhoto { get; set; }
    }

    // Customer ViewModels
    public class CustomerDetailViewModel
    {
        public User Customer { get; set; } = null!;
        public int TotalListings { get; set; }
        public int ActiveListings { get; set; }
        public int PendingListings { get; set; }
        public int TotalPurchases { get; set; }
        public int TotalReviews { get; set; }
        public List<Listing> RecentListings { get; set; } = new List<Listing>();
        public List<PackagePurchase> RecentPurchases { get; set; } = new List<PackagePurchase>();
    }

    // ListingBrand ViewModels
    public class ListingBrandCreateViewModel
    {
        [Required(ErrorMessage = "Brand name is required")]
        [StringLength(255, ErrorMessage = "Brand name cannot exceed 255 characters")]
        public string ListingBrandName { get; set; } = "";

        [StringLength(255, ErrorMessage = "Brand slug cannot exceed 255 characters")]
        public string? ListingBrandSlug { get; set; }

        public IFormFile? ListingBrandPhoto { get; set; }

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }
    }

    public class ListingBrandEditViewModel : ListingBrandCreateViewModel
    {
        public int Id { get; set; }
        public string? ExistingPhoto { get; set; }
    }

    // Package ViewModels
    public class PackageCreateViewModel
    {
        [Required(ErrorMessage = "Package name is required")]
        [StringLength(255, ErrorMessage = "Package name cannot exceed 255 characters")]
        public string PackageName { get; set; } = "";

        [Required(ErrorMessage = "Package price is required")]
        [Range(0, double.MaxValue, ErrorMessage = "Package price must be a positive number")]
        public decimal PackagePrice { get; set; }

        [StringLength(50, ErrorMessage = "Display price cannot exceed 50 characters")]
        public string? PackageDisplayPrice { get; set; }

        [Required(ErrorMessage = "Allowed days is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Allowed days must be at least 1")]
        public int PackageAllowedDays { get; set; }

        [Required(ErrorMessage = "Allowed listings is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Allowed listings must be at least 1")]
        public int PackageAllowedListings { get; set; }

        [Required(ErrorMessage = "Allowed photos is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Allowed photos must be at least 1")]
        public int PackageAllowedPhotos { get; set; }

        [Required(ErrorMessage = "Allowed videos is required")]
        [Range(0, int.MaxValue, ErrorMessage = "Allowed videos must be 0 or more")]
        public int PackageAllowedVideos { get; set; }

        [Required(ErrorMessage = "Allowed amenities is required")]
        [Range(0, int.MaxValue, ErrorMessage = "Allowed amenities must be 0 or more")]
        public int PackageAllowedAmenities { get; set; }

        [Required(ErrorMessage = "Allowed additional features is required")]
        [Range(0, int.MaxValue, ErrorMessage = "Allowed additional features must be 0 or more")]
        public int PackageAllowedAdditionalFeatures { get; set; }

        [Required(ErrorMessage = "Package order is required")]
        [Range(1, 100, ErrorMessage = "Package order must be between 1 and 100")]
        public int PackageOrder { get; set; } = 1;
    }

    public class PackageEditViewModel : PackageCreateViewModel
    {
        public int Id { get; set; }
    }

    // Profile ViewModels
    public class AdminProfileChangeViewModel
    {
        [StringLength(255, ErrorMessage = "Name cannot exceed 255 characters")]
        public string? Name { get; set; }

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
        public string Email { get; set; } = "";

        [StringLength(20, ErrorMessage = "Phone cannot exceed 20 characters")]
        public string? Phone { get; set; }

        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string? Country { get; set; }

        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string? Address { get; set; }

        [StringLength(100, ErrorMessage = "State cannot exceed 100 characters")]
        public string? State { get; set; }

        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string? City { get; set; }

        [StringLength(20, ErrorMessage = "ZIP code cannot exceed 20 characters")]
        public string? Zip { get; set; }

        [StringLength(255, ErrorMessage = "Website cannot exceed 255 characters")]
        public string? Website { get; set; }

        [StringLength(255, ErrorMessage = "Facebook URL cannot exceed 255 characters")]
        public string? Facebook { get; set; }

        [StringLength(255, ErrorMessage = "Twitter URL cannot exceed 255 characters")]
        public string? Twitter { get; set; }

        [StringLength(255, ErrorMessage = "LinkedIn URL cannot exceed 255 characters")]
        public string? LinkedIn { get; set; }

        [StringLength(255, ErrorMessage = "Instagram URL cannot exceed 255 characters")]
        public string? Instagram { get; set; }

        [StringLength(255, ErrorMessage = "Pinterest URL cannot exceed 255 characters")]
        public string? Pinterest { get; set; }

        [StringLength(255, ErrorMessage = "YouTube URL cannot exceed 255 characters")]
        public string? Youtube { get; set; }

        public IFormFile? Photo { get; set; }
        public string? ExistingPhoto { get; set; }

        public IFormFile? Banner { get; set; }
        public string? ExistingBanner { get; set; }
    }

    public class AdminPhotoChangeViewModel
    {
        [Required(ErrorMessage = "Photo is required")]
        public IFormFile Photo { get; set; } = null!;
        public string? ExistingPhoto { get; set; }
    }

    public class AdminBannerChangeViewModel
    {
        [Required(ErrorMessage = "Banner is required")]
        public IFormFile Banner { get; set; } = null!;
        public string? ExistingBanner { get; set; }
    }

    public class AdminChangePasswordViewModel
    {
        [Required(ErrorMessage = "Current password is required")]
        public string CurrentPassword { get; set; } = "";

        [Required(ErrorMessage = "New password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be at least 6 characters long")]
        public string NewPassword { get; set; } = "";

        [Required(ErrorMessage = "Please confirm your password")]
        [Compare("NewPassword", ErrorMessage = "Passwords do not match")]
        public string ConfirmPassword { get; set; } = "";
    }

    // Amenity ViewModels
    public class AmenityCreateViewModel
    {
        [Required(ErrorMessage = "Amenity name is required")]
        [StringLength(255, ErrorMessage = "Amenity name cannot exceed 255 characters")]
        public string AmenityName { get; set; } = "";

        [StringLength(255, ErrorMessage = "Amenity slug cannot exceed 255 characters")]
        public string? AmenitySlug { get; set; }
    }

    public class AmenityEditViewModel : AmenityCreateViewModel
    {
        public int Id { get; set; }
    }

    // ListingLocation ViewModels
    public class ListingLocationCreateViewModel
    {
        [Required(ErrorMessage = "Location name is required")]
        [StringLength(255, ErrorMessage = "Location name cannot exceed 255 characters")]
        public string ListingLocationName { get; set; } = "";

        [StringLength(255, ErrorMessage = "Location slug cannot exceed 255 characters")]
        public string? ListingLocationSlug { get; set; }

        public IFormFile? ListingLocationPhoto { get; set; }

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }
    }

    public class ListingLocationEditViewModel : ListingLocationCreateViewModel
    {
        public int Id { get; set; }
        public string? ExistingPhoto { get; set; }
    }

    // Currency ViewModels
    public class CurrencyCreateViewModel
    {
        [Required(ErrorMessage = "Currency name is required")]
        [StringLength(255, ErrorMessage = "Currency name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        [Required(ErrorMessage = "Currency symbol is required")]
        [StringLength(10, ErrorMessage = "Currency symbol cannot exceed 10 characters")]
        public string Symbol { get; set; } = "";

        [Required(ErrorMessage = "Currency value is required")]
        [Range(0.01, 999999.99, ErrorMessage = "Currency value must be between 0.01 and 999999.99")]
        public decimal Value { get; set; } = 1.00m;

        public bool IsDefault { get; set; } = false;
    }

    public class CurrencyEditViewModel : CurrencyCreateViewModel
    {
        public int Id { get; set; }
    }

    // DynamicPage ViewModels
    public class DynamicPageCreateViewModel
    {
        [Required(ErrorMessage = "Dynamic page name is required")]
        [StringLength(255, ErrorMessage = "Dynamic page name cannot exceed 255 characters")]
        public string DynamicPageName { get; set; } = "";

        [StringLength(255, ErrorMessage = "Dynamic page slug cannot exceed 255 characters")]
        public string? DynamicPageSlug { get; set; }

        public string? DynamicPageContent { get; set; }

        [Required(ErrorMessage = "Dynamic page banner is required")]
        public IFormFile? DynamicPageBanner { get; set; }

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }
    }

    public class DynamicPageEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Dynamic page name is required")]
        [StringLength(255, ErrorMessage = "Dynamic page name cannot exceed 255 characters")]
        public string DynamicPageName { get; set; } = "";

        [StringLength(255, ErrorMessage = "Dynamic page slug cannot exceed 255 characters")]
        public string? DynamicPageSlug { get; set; }

        public string? DynamicPageContent { get; set; }

        public IFormFile? DynamicPageBanner { get; set; }

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public string? ExistingBanner { get; set; }
    }

    // EmailTemplate ViewModels
    public class EmailTemplateEditViewModel
    {
        public int Id { get; set; }

        public string EtName { get; set; } = "";

        [Required(ErrorMessage = "Email subject is required")]
        [StringLength(255, ErrorMessage = "Email subject cannot exceed 255 characters")]
        public string EtSubject { get; set; } = "";

        [Required(ErrorMessage = "Email content is required")]
        public string EtContent { get; set; } = "";
    }

    // SocialMedia ViewModels
    public class SocialMediaCreateViewModel
    {
        [Required(ErrorMessage = "Social media URL is required")]
        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "Social media URL cannot exceed 500 characters")]
        public string SocialUrl { get; set; } = "";

        [Required(ErrorMessage = "Social media icon is required")]
        [StringLength(100, ErrorMessage = "Social media icon cannot exceed 100 characters")]
        public string SocialIcon { get; set; } = "";

        [Range(0, 32767, ErrorMessage = "Social order must be between 0 and 32767")]
        public int SocialOrder { get; set; } = 0;
    }

    public class SocialMediaEditViewModel : SocialMediaCreateViewModel
    {
        public int Id { get; set; }
    }

    // PurchaseHistory ViewModels
    public class PurchaseHistoryDetailViewModel
    {
        public PackagePurchase PurchaseDetail { get; set; } = null!;
        public GeneralSetting GeneralSettings { get; set; } = null!;
    }

    public class PurchaseHistoryInvoiceViewModel
    {
        public PackagePurchase PurchaseDetail { get; set; } = null!;
        public GeneralSetting GeneralSettings { get; set; } = null!;
    }

    // Advertisement ViewModels
    public class AdvertisementEditViewModel
    {
        public int Id { get; set; }

        // Above Brand Section
        public IFormFile? AboveBrand1 { get; set; }
        public IFormFile? AboveBrand2 { get; set; }

        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "URL cannot exceed 500 characters")]
        public string? AboveBrand1Url { get; set; }

        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "URL cannot exceed 500 characters")]
        public string? AboveBrand2Url { get; set; }

        public string AboveBrandStatus { get; set; } = Common.PageStatus.Hide;

        // Above Featured Listing Section
        public IFormFile? AboveFeaturedListing1 { get; set; }
        public IFormFile? AboveFeaturedListing2 { get; set; }

        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "URL cannot exceed 500 characters")]
        public string? AboveFeaturedListing1Url { get; set; }

        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "URL cannot exceed 500 characters")]
        public string? AboveFeaturedListing2Url { get; set; }

        public string AboveFeaturedListingStatus { get; set; } = Common.PageStatus.Hide;

        // Above Location Section
        public IFormFile? AboveLocation1 { get; set; }
        public IFormFile? AboveLocation2 { get; set; }

        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "URL cannot exceed 500 characters")]
        public string? AboveLocation1Url { get; set; }

        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "URL cannot exceed 500 characters")]
        public string? AboveLocation2Url { get; set; }

        public string AboveLocationStatus { get; set; } = Common.PageStatus.Hide;

        // Existing files (for display purposes)
        public string? ExistingAboveBrand1 { get; set; }
        public string? ExistingAboveBrand2 { get; set; }
        public string? ExistingAboveFeaturedListing1 { get; set; }
        public string? ExistingAboveFeaturedListing2 { get; set; }
        public string? ExistingAboveLocation1 { get; set; }
        public string? ExistingAboveLocation2 { get; set; }
    }

    // Language ViewModels
    public class LanguageTextViewModel
    {
        public Dictionary<string, string> LanguageData { get; set; } = new Dictionary<string, string>();
        public List<string> Keys { get; set; } = new List<string>();
        public List<string> Values { get; set; } = new List<string>();
        public string TextType { get; set; } = "";
    }

    // Page ViewModels
    public class PageAboutEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Page name is required")]
        [StringLength(255, ErrorMessage = "Page name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        public string? Detail { get; set; }

        public IFormFile? Banner { get; set; }

        public string Status { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public string? ExistingBanner { get; set; }
    }

    public class PageContactEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Page name is required")]
        [StringLength(255, ErrorMessage = "Page name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        public string? Detail { get; set; }

        public IFormFile? Banner { get; set; }

        public string Status { get; set; } = Common.PageStatus.Show;

        [StringLength(500, ErrorMessage = "Contact address cannot exceed 500 characters")]
        public string? ContactAddress { get; set; }

        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        [StringLength(255, ErrorMessage = "Contact email cannot exceed 255 characters")]
        public string? ContactEmail { get; set; }

        [StringLength(50, ErrorMessage = "Contact phone cannot exceed 50 characters")]
        public string? ContactPhone { get; set; }

        public string? ContactMap { get; set; }

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public string? ExistingBanner { get; set; }
    }

    // Footer ViewModels
    public class FooterItemCreateViewModel
    {
        [Required(ErrorMessage = "Item URL is required")]
        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "Item URL cannot exceed 500 characters")]
        public string ItemUrl { get; set; } = "";

        [StringLength(100, ErrorMessage = "Item icon cannot exceed 100 characters")]
        public string? ItemIcon { get; set; }

        [Range(0, 32767, ErrorMessage = "Item order must be between 0 and 32767")]
        public int ItemOrder { get; set; } = 0;
    }

    public class FooterItemEditViewModel : FooterItemCreateViewModel
    {
        public int Id { get; set; }
    }

    // PageHome ViewModels
    public class PageHomeEditViewModel
    {
        public int Id { get; set; }

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        [StringLength(255, ErrorMessage = "Search heading cannot exceed 255 characters")]
        public string? SearchHeading { get; set; }

        [StringLength(500, ErrorMessage = "Search text cannot exceed 500 characters")]
        public string? SearchText { get; set; }

        [StringLength(255, ErrorMessage = "Brand heading cannot exceed 255 characters")]
        public string? BrandHeading { get; set; }

        [StringLength(255, ErrorMessage = "Brand subheading cannot exceed 255 characters")]
        public string? BrandSubheading { get; set; }

        [Range(1, 100, ErrorMessage = "Brand total must be between 1 and 100")]
        public int BrandTotal { get; set; } = 10;

        public string BrandStatus { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "Video heading cannot exceed 255 characters")]
        public string? VideoHeading { get; set; }

        [StringLength(500, ErrorMessage = "Video text cannot exceed 500 characters")]
        public string? VideoText { get; set; }

        [StringLength(255, ErrorMessage = "Video YouTube ID cannot exceed 255 characters")]
        public string? VideoYoutubeId { get; set; }

        public string VideoStatus { get; set; } = Common.PageStatus.Hide;

        [StringLength(255, ErrorMessage = "Listing heading cannot exceed 255 characters")]
        public string? ListingHeading { get; set; }

        [StringLength(255, ErrorMessage = "Listing subheading cannot exceed 255 characters")]
        public string? ListingSubheading { get; set; }

        [Range(1, 100, ErrorMessage = "Listing total must be between 1 and 100")]
        public int ListingTotal { get; set; } = 8;

        public string ListingStatus { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "Testimonial heading cannot exceed 255 characters")]
        public string? TestimonialHeading { get; set; }

        [StringLength(255, ErrorMessage = "Testimonial subheading cannot exceed 255 characters")]
        public string? TestimonialSubheading { get; set; }

        public string TestimonialStatus { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "Location heading cannot exceed 255 characters")]
        public string? LocationHeading { get; set; }

        [StringLength(255, ErrorMessage = "Location subheading cannot exceed 255 characters")]
        public string? LocationSubheading { get; set; }

        [Range(1, 100, ErrorMessage = "Location total must be between 1 and 100")]
        public int LocationTotal { get; set; } = 6;

        public string LocationStatus { get; set; } = Common.PageStatus.Show;

        // File uploads
        public IFormFile? SearchBackground { get; set; }
        public IFormFile? TestimonialBackground { get; set; }
        public IFormFile? VideoBackground { get; set; }

        // Existing files
        public string? ExistingSearchBackground { get; set; }
        public string? ExistingTestimonialBackground { get; set; }
        public string? ExistingVideoBackground { get; set; }
    }

    // PagePricing ViewModels
    public class PagePricingEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Page name is required")]
        [StringLength(255, ErrorMessage = "Page name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        public string? Detail { get; set; }

        public IFormFile? Banner { get; set; }

        public string Status { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public string? ExistingBanner { get; set; }
    }

    // PagePrivacy ViewModels
    public class PagePrivacyEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Page name is required")]
        [StringLength(255, ErrorMessage = "Page name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        public string? Detail { get; set; }

        public IFormFile? Banner { get; set; }

        public string Status { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public string? ExistingBanner { get; set; }
    }

    // PageTerm ViewModels
    public class PageTermEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Page name is required")]
        [StringLength(255, ErrorMessage = "Page name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        public string? Detail { get; set; }

        public IFormFile? Banner { get; set; }

        public string Status { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public string? ExistingBanner { get; set; }
    }

    // PageFaq ViewModels
    public class PageFaqEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Page name is required")]
        [StringLength(255, ErrorMessage = "Page name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        public string? Detail { get; set; }

        public IFormFile? Banner { get; set; }

        public string Status { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public string? ExistingBanner { get; set; }
    }

    // PageBlog ViewModels
    public class PageBlogEditViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Page name is required")]
        [StringLength(255, ErrorMessage = "Page name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        public string? Detail { get; set; }

        public IFormFile? Banner { get; set; }

        public string Status { get; set; } = Common.PageStatus.Show;

        [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
        public string? SeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
        public string? SeoMetaDescription { get; set; }

        public string? ExistingBanner { get; set; }
    }

    // PageOther ViewModels
    public class PageOtherEditViewModel
    {
        public int Id { get; set; }

        [StringLength(255, ErrorMessage = "Login page SEO title cannot exceed 255 characters")]
        public string? LoginPageSeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "Login page SEO meta description cannot exceed 500 characters")]
        public string? LoginPageSeoMetaDescription { get; set; }

        public IFormFile? LoginPageBanner { get; set; }

        [StringLength(255, ErrorMessage = "Registration page SEO title cannot exceed 255 characters")]
        public string? RegistrationPageSeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "Registration page SEO meta description cannot exceed 500 characters")]
        public string? RegistrationPageSeoMetaDescription { get; set; }

        public IFormFile? RegistrationPageBanner { get; set; }

        [StringLength(255, ErrorMessage = "Forget password page SEO title cannot exceed 255 characters")]
        public string? ForgetPasswordPageSeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "Forget password page SEO meta description cannot exceed 500 characters")]
        public string? ForgetPasswordPageSeoMetaDescription { get; set; }

        public IFormFile? ForgetPasswordPageBanner { get; set; }

        [StringLength(255, ErrorMessage = "Customer panel page SEO title cannot exceed 255 characters")]
        public string? CustomerPanelPageSeoTitle { get; set; }

        [StringLength(500, ErrorMessage = "Customer panel page SEO meta description cannot exceed 500 characters")]
        public string? CustomerPanelPageSeoMetaDescription { get; set; }

        public IFormFile? CustomerPanelPageBanner { get; set; }

        // Existing files
        public string? ExistingLoginPageBanner { get; set; }
        public string? ExistingRegistrationPageBanner { get; set; }
        public string? ExistingForgetPasswordPageBanner { get; set; }
        public string? ExistingCustomerPanelPageBanner { get; set; }
    }
}
