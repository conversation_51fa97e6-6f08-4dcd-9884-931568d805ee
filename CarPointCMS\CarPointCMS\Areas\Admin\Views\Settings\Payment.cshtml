@using CarPointCMS.Models.Entities
@model GeneralSetting
@{
    ViewData["Title"] = "Edit Payment Setting";
}

<h1 class="h3 mb-3 text-gray-800">Edit Payment Setting</h1>

<form asp-area="Admin" asp-controller="Settings" asp-action="Payment" method="post">
    @Html.AntiForgeryToken()

    <div class="card shadow mb-4 t-left">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 col-sm-12">
                    <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <a class="nav-link active" id="p1_tab" data-toggle="pill" href="#p1" role="tab" aria-controls="p1" aria-selected="true">PayPal</a>
                        <a class="nav-link" id="p2_tab" data-toggle="pill" href="#p2" role="tab" aria-controls="p2" aria-selected="false">Stripe</a>
                        <a class="nav-link" id="p3_tab" data-toggle="pill" href="#p3" role="tab" aria-controls="p3" aria-selected="false">Razorpay</a>
                        <a class="nav-link" id="p4_tab" data-toggle="pill" href="#p4" role="tab" aria-controls="p4" aria-selected="false">Flutterwave</a>
                        <a class="nav-link" id="p5_tab" data-toggle="pill" href="#p5" role="tab" aria-controls="p5" aria-selected="false">Mollie</a>
                    </div>
                </div>
                <div class="col-md-9 col-sm-12">
                    <div class="tab-content" id="v-pills-tabContent">

                        <!-- PayPal Tab -->
                        <div class="tab-pane fade show active" id="p1" role="tabpanel" aria-labelledby="p1_tab">
                            <div class="form-group">
                                <label for="">PayPal Environment</label>
                                <select name="PaypalEnvironment" class="form-control">
                                    @if (Model.PaypalEnvironment == "sandbox")
                                    {
                                        <option value="sandbox" selected>Sandbox</option>
                                        <option value="production">Production</option>
                                    }
                                    else
                                    {
                                        <option value="sandbox">Sandbox</option>
                                        <option value="production" selected>Production</option>
                                    }
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="">PayPal Client ID</label>
                                <input type="text" class="form-control" name="PaypalClientId" value="@Model.PaypalClientId">
                            </div>
                            <div class="form-group">
                                <label for="">PayPal Secret Key</label>
                                <input type="text" class="form-control" name="PaypalSecretKey" value="@Model.PaypalSecretKey">
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="PaypalStatus" class="form-control">
                                    @if (Model.PaypalStatus)
                                    {
                                        <option value="true" selected>Show</option>
                                        <option value="false">Hide</option>
                                    }
                                    else
                                    {
                                        <option value="true">Show</option>
                                        <option value="false" selected>Hide</option>
                                    }
                                </select>
                            </div>
                        </div>

                        <!-- Stripe Tab -->
                        <div class="tab-pane fade" id="p2" role="tabpanel" aria-labelledby="p2_tab">
                            <div class="form-group">
                                <label for="">Stripe Public Key</label>
                                <input type="text" class="form-control" name="StripePublicKey" value="@Model.StripePublicKey">
                            </div>
                            <div class="form-group">
                                <label for="">Stripe Secret Key</label>
                                <input type="text" class="form-control" name="StripeSecretKey" value="@Model.StripeSecretKey">
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="StripeStatus" class="form-control">
                                    @if (Model.StripeStatus)
                                    {
                                        <option value="true" selected>Show</option>
                                        <option value="false">Hide</option>
                                    }
                                    else
                                    {
                                        <option value="true">Show</option>
                                        <option value="false" selected>Hide</option>
                                    }
                                </select>
                            </div>
                        </div>

                        <!-- Razorpay Tab -->
                        <div class="tab-pane fade" id="p3" role="tabpanel" aria-labelledby="p3_tab">
                            <div class="form-group">
                                <label for="">Razorpay Key ID</label>
                                <input type="text" class="form-control" name="RazorpayKeyId" value="@Model.RazorpayKeyId">
                            </div>
                            <div class="form-group">
                                <label for="">Razorpay Key Secret</label>
                                <input type="text" class="form-control" name="RazorpayKeySecret" value="@Model.RazorpayKeySecret">
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="RazorpayStatus" class="form-control">
                                    @if (Model.RazorpayStatus)
                                    {
                                        <option value="true" selected>Show</option>
                                        <option value="false">Hide</option>
                                    }
                                    else
                                    {
                                        <option value="true">Show</option>
                                        <option value="false" selected>Hide</option>
                                    }
                                </select>
                            </div>
                        </div>

                        <!-- Flutterwave Tab -->
                        <div class="tab-pane fade" id="p4" role="tabpanel" aria-labelledby="p4_tab">
                            <div class="form-group">
                                <label for="">Flutterwave Country</label>
                                <input type="text" class="form-control" name="FlutterwaveCountry" value="@Model.FlutterwaveCountry">
                            </div>
                            <div class="form-group">
                                <label for="">Flutterwave Public Key</label>
                                <input type="text" class="form-control" name="FlutterwavePublicKey" value="@Model.FlutterwavePublicKey">
                            </div>
                            <div class="form-group">
                                <label for="">Flutterwave Secret Key</label>
                                <input type="text" class="form-control" name="FlutterwaveSecretKey" value="@Model.FlutterwaveSecretKey">
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="FlutterwaveStatus" class="form-control">
                                    @if (Model.FlutterwaveStatus)
                                    {
                                        <option value="true" selected>Show</option>
                                        <option value="false">Hide</option>
                                    }
                                    else
                                    {
                                        <option value="true">Show</option>
                                        <option value="false" selected>Hide</option>
                                    }
                                </select>
                            </div>
                        </div>

                        <!-- Mollie Tab -->
                        <div class="tab-pane fade" id="p5" role="tabpanel" aria-labelledby="p5_tab">
                            <div class="form-group">
                                <label for="">Mollie API Key</label>
                                <input type="text" class="form-control" name="MollieApiKey" value="@Model.MollieApiKey">
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="MollieStatus" class="form-control">
                                    @if (Model.MollieStatus)
                                    {
                                        <option value="true" selected>Show</option>
                                        <option value="false">Hide</option>
                                    }
                                    else
                                    {
                                        <option value="true">Show</option>
                                        <option value="false" selected>Hide</option>
                                    }
                                </select>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <button type="submit" class="btn btn-success btn-block">Update</button>
</form>
