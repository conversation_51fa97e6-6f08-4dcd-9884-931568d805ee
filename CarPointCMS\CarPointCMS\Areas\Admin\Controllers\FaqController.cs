using Microsoft.AspNetCore.Mvc;
using CarPointCMS.Data;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Models.Entities;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using CarPointCMS.Attributes;
using Microsoft.EntityFrameworkCore;

namespace CarPointCMS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminOnly]
    public class FaqController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public FaqController(ApplicationDbContext context, IAuthenticationService authService)
            : base(authService)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var faqs = await _context.Faqs
                .OrderBy(f => f.FaqQuestion)
                .ToListAsync();

            return View(faqs);
        }

        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Create(FaqCreateViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var faq = new Faq
                {
                    FaqQuestion = model.FaqQuestion,
                    FaqAnswer = model.FaqAnswer,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Faqs.Add(faq);
                await _context.SaveChangesAsync();

                SetSuccessMessage("FAQ has been added successfully.");
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while saving the FAQ.");
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            var faq = await _context.Faqs.FindAsync(id);
            if (faq == null)
            {
                SetErrorMessage("FAQ not found.");
                return RedirectToAction("Index");
            }

            var model = new FaqEditViewModel
            {
                Id = faq.Id,
                FaqQuestion = faq.FaqQuestion,
                FaqAnswer = faq.FaqAnswer
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(FaqEditViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var faq = await _context.Faqs.FindAsync(model.Id);
                if (faq == null)
                {
                    SetErrorMessage("FAQ not found.");
                    return RedirectToAction("Index");
                }

                faq.FaqQuestion = model.FaqQuestion;
                faq.FaqAnswer = model.FaqAnswer;
                faq.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                SetSuccessMessage("FAQ has been updated successfully.");
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while updating the FAQ.");
                return View(model);
            }
        }

        [HttpPost]
        public async Task<IActionResult> Delete(int id)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            try
            {
                var faq = await _context.Faqs.FindAsync(id);
                if (faq == null)
                {
                    TempData["error"] = "FAQ not found.";
                    return RedirectToAction("Index");
                }

                _context.Faqs.Remove(faq);
                await _context.SaveChangesAsync();

                TempData["success"] = "FAQ has been deleted successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while deleting the FAQ.";
                return RedirectToAction("Index");
            }
        }
    }
}
