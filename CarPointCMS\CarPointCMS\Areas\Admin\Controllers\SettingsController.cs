using Microsoft.AspNetCore.Mvc;
using CarPointCMS.Data;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Models.Entities;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using CarPointCMS.Attributes;
using Microsoft.EntityFrameworkCore;

namespace CarPointCMS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminOnly]
    public class SettingsController : BaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public SettingsController(ApplicationDbContext context, IWebHostEnvironment webHostEnvironment, IAuthenticationService authService)
            : base(authService)
        {
            _context = context;
            _webHostEnvironment = webHostEnvironment;
        }

        [HttpGet]
        public async Task<IActionResult> General()
        {
            var setting = await _context.GeneralSettings.FirstOrDefaultAsync();
            if (setting == null)
            {
                // Create default settings if none exist
                setting = new GeneralSetting
                {
                    TopEmail = "<EMAIL>",
                    TopPhone = "******-567-8900",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                _context.GeneralSettings.Add(setting);
                await _context.SaveChangesAsync();
            }

            return View(setting);
        }

        [HttpPost]
        public async Task<IActionResult> General(GeneralSetting model, IFormFile? Logo, IFormFile? Favicon, string? CurrentLogo, string? CurrentFavicon)
        {
            try
            {
                var setting = await _context.GeneralSettings.FirstOrDefaultAsync();
                if (setting == null)
                {
                    setting = new GeneralSetting();
                    _context.GeneralSettings.Add(setting);
                }

                // Handle logo upload
                if (Logo != null && Logo.Length > 0)
                {
                    var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                    var fileExtension = Path.GetExtension(Logo.FileName).ToLowerInvariant();

                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        SetErrorMessage("Invalid logo file type. Only JPG, JPEG, PNG, and GIF files are allowed.");
                        return View(model);
                    }

                    if (Logo.Length > 5 * 1024 * 1024) // 5MB limit
                    {
                        SetErrorMessage("Logo file size cannot exceed 5MB.");
                        return View(model);
                    }

                    // Delete old logo if exists
                    if (!string.IsNullOrEmpty(CurrentLogo))
                    {
                        var oldLogoPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads", "site_photos", CurrentLogo);
                        if (System.IO.File.Exists(oldLogoPath))
                        {
                            System.IO.File.Delete(oldLogoPath);
                        }
                    }

                    // Save new logo
                    var logoFileName = $"logo_{DateTime.Now:yyyyMMddHHmmss}{fileExtension}";
                    var logoPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads", "site_photos", logoFileName);

                    Directory.CreateDirectory(Path.GetDirectoryName(logoPath)!);

                    using (var stream = new FileStream(logoPath, FileMode.Create))
                    {
                        await Logo.CopyToAsync(stream);
                    }

                    setting.Logo = logoFileName;
                }
                else
                {
                    setting.Logo = CurrentLogo;
                }

                // Handle favicon upload
                if (Favicon != null && Favicon.Length > 0)
                {
                    var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".ico" };
                    var fileExtension = Path.GetExtension(Favicon.FileName).ToLowerInvariant();

                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        SetErrorMessage("Invalid favicon file type. Only JPG, JPEG, PNG, GIF, and ICO files are allowed.");
                        return View(model);
                    }

                    if (Favicon.Length > 2 * 1024 * 1024) // 2MB limit
                    {
                        SetErrorMessage("Favicon file size cannot exceed 2MB.");
                        return View(model);
                    }

                    // Delete old favicon if exists
                    if (!string.IsNullOrEmpty(CurrentFavicon))
                    {
                        var oldFaviconPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads", "site_photos", CurrentFavicon);
                        if (System.IO.File.Exists(oldFaviconPath))
                        {
                            System.IO.File.Delete(oldFaviconPath);
                        }
                    }

                    // Save new favicon
                    var faviconFileName = $"favicon_{DateTime.Now:yyyyMMddHHmmss}{fileExtension}";
                    var faviconPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads", "site_photos", faviconFileName);

                    Directory.CreateDirectory(Path.GetDirectoryName(faviconPath)!);

                    using (var stream = new FileStream(faviconPath, FileMode.Create))
                    {
                        await Favicon.CopyToAsync(stream);
                    }

                    setting.Favicon = faviconFileName;
                }
                else
                {
                    setting.Favicon = CurrentFavicon;
                }

                // Update all other settings
                setting.TopPhone = model.TopPhone;
                setting.TopEmail = model.TopEmail;
                setting.FooterAddress = model.FooterAddress;
                setting.FooterPhone = model.FooterPhone;
                setting.FooterEmail = model.FooterEmail;
                setting.FooterCopyright = model.FooterCopyright;
                setting.FooterFacebook = model.FooterFacebook;
                setting.FooterTwitter = model.FooterTwitter;
                setting.FooterLinkedin = model.FooterLinkedin;
                setting.FooterInstagram = model.FooterInstagram;
                setting.FooterYoutube = model.FooterYoutube;
                setting.ThemeColor = model.ThemeColor;
                setting.CustomerListingOption = model.CustomerListingOption;
                setting.TawkLiveChatStatus = model.TawkLiveChatStatus;
                setting.TawkLiveChatPropertyId = model.TawkLiveChatPropertyId;
                setting.GoogleAnalyticStatus = model.GoogleAnalyticStatus;
                setting.GoogleAnalyticTrackingId = model.GoogleAnalyticTrackingId;
                setting.GoogleRecaptchaSiteKey = model.GoogleRecaptchaSiteKey;
                setting.GoogleRecaptchaStatus = model.GoogleRecaptchaStatus;
                setting.CookieConsentStatus = model.CookieConsentStatus;
                setting.CookieConsentMessage = model.CookieConsentMessage;
                setting.CookieConsentButtonText = model.CookieConsentButtonText;
                setting.CookieConsentBgColor = model.CookieConsentBgColor;
                setting.CookieConsentTextColor = model.CookieConsentTextColor;
                setting.CookieConsentButtonBgColor = model.CookieConsentButtonBgColor;
                setting.CookieConsentButtonTextColor = model.CookieConsentButtonTextColor;
                setting.LayoutDirection = model.LayoutDirection;
                setting.UpdatedAt = DateTime.UtcNow;

                if (setting.Id == 0)
                {
                    setting.CreatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                SetSuccessMessage("Settings have been updated successfully.");
                return RedirectToActionInArea("General");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while updating settings.");
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> Payment()
        {
            var setting = await _context.GeneralSettings.FirstOrDefaultAsync();
            if (setting == null)
            {
                // Create default settings if none exist
                setting = new GeneralSetting
                {
                    PaypalEnvironment = "sandbox",
                    PaypalStatus = false,
                    StripeStatus = false,
                    RazorpayStatus = false,
                    FlutterwaveStatus = false,
                    MollieStatus = false,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                _context.GeneralSettings.Add(setting);
                await _context.SaveChangesAsync();
            }

            return View(setting);
        }

        [HttpPost]
        public async Task<IActionResult> Payment(GeneralSetting model)
        {
            try
            {
                var setting = await _context.GeneralSettings.FirstOrDefaultAsync();
                if (setting == null)
                {
                    setting = new GeneralSetting();
                    _context.GeneralSettings.Add(setting);
                }

                // Update PayPal settings
                setting.PaypalEnvironment = model.PaypalEnvironment;
                setting.PaypalClientId = model.PaypalClientId;
                setting.PaypalSecretKey = model.PaypalSecretKey;
                setting.PaypalStatus = model.PaypalStatus;

                // Update Stripe settings
                setting.StripePublicKey = model.StripePublicKey;
                setting.StripeSecretKey = model.StripeSecretKey;
                setting.StripeStatus = model.StripeStatus;

                // Update Razorpay settings
                setting.RazorpayKeyId = model.RazorpayKeyId;
                setting.RazorpayKeySecret = model.RazorpayKeySecret;
                setting.RazorpayStatus = model.RazorpayStatus;

                // Update Flutterwave settings
                setting.FlutterwaveCountry = model.FlutterwaveCountry;
                setting.FlutterwavePublicKey = model.FlutterwavePublicKey;
                setting.FlutterwaveSecretKey = model.FlutterwaveSecretKey;
                setting.FlutterwaveStatus = model.FlutterwaveStatus;

                // Update Mollie settings
                setting.MollieApiKey = model.MollieApiKey;
                setting.MollieStatus = model.MollieStatus;

                setting.UpdatedAt = DateTime.UtcNow;

                if (setting.Id == 0)
                {
                    setting.CreatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                SetSuccessMessage("Payment settings have been updated successfully.");
                return RedirectToActionInArea("Payment");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while updating payment settings.");
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> EmailTemplates()
        {
            var emailTemplates = await _context.EmailTemplates
                .OrderBy(et => et.EtName)
                .ToListAsync();

            return View(emailTemplates);
        }

        [HttpGet]
        public async Task<IActionResult> EditEmailTemplate(int id)
        {
            var emailTemplate = await _context.EmailTemplates.FindAsync(id);
            if (emailTemplate == null)
            {
                SetErrorMessage("Email template not found.");
                return RedirectToActionInArea("EmailTemplates");
            }

            var model = new EmailTemplateEditViewModel
            {
                Id = emailTemplate.Id,
                EtName = emailTemplate.EtName,
                EtSubject = emailTemplate.EtSubject,
                EtContent = emailTemplate.EtContent
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> EditEmailTemplate(EmailTemplateEditViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var emailTemplate = await _context.EmailTemplates.FindAsync(model.Id);
                if (emailTemplate == null)
                {
                    SetErrorMessage("Email template not found.");
                    return RedirectToActionInArea("EmailTemplates");
                }

                emailTemplate.EtSubject = model.EtSubject;
                emailTemplate.EtContent = model.EtContent;
                emailTemplate.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                SetSuccessMessage("Email template has been updated successfully.");
                return RedirectToActionInArea("EmailTemplates");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while updating the email template.");
                return View(model);
            }
        }

        // Helper methods removed - now using BaseController methods
    }
}
