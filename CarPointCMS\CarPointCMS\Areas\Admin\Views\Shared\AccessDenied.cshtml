@{
    ViewData["Title"] = "Access Denied";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8 col-sm-10">
            <div class="card shadow-lg border-0 mt-5">
                <div class="card-header bg-danger text-white text-center py-4">
                    <div class="error-icon mb-3">
                        <i class="fas fa-shield-alt fa-4x"></i>
                    </div>
                    <h2 class="mb-0 font-weight-bold">Access Denied</h2>
                    <small class="text-light">Error Code: 403</small>
                </div>
                
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <h4 class="text-dark mb-3">Insufficient Permissions</h4>
                        <p class="text-muted lead">
                            You don't have the required permissions to access this resource. 
                            This area is restricted to authorized administrators only.
                        </p>
                    </div>
                    
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Access Restricted:</strong> This action requires administrator privileges.
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6 mb-3">
                            <a href="javascript:history.back()" class="btn btn-secondary btn-block">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Go Back
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="@Url.Action("Index", "Home", new { area = "Admin" })" class="btn btn-primary btn-block">
                                <i class="fas fa-tachometer-alt mr-2"></i>
                                Dashboard
                            </a>
                        </div>
                    </div>
                    
                    <div class="row mt-2">
                        <div class="col-12">
                            <a href="@Url.Action("Login", "Auth", new { area = "Admin" })" class="btn btn-outline-success btn-block">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                Login as Administrator
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer bg-light text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle mr-1"></i>
                        If you believe this is an error, please contact the system administrator.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .error-icon {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }
    
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .btn {
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
</style>
