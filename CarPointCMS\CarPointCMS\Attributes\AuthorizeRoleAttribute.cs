using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using CarPointCMS.Common;

namespace CarPointCMS.Attributes
{
    public class AuthorizeRoleAttribute : Attribute, IAuthorizationFilter
    {
        private readonly string[] _roles;

        public AuthorizeRoleAttribute(params string[] roles)
        {
            _roles = roles;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var user = context.HttpContext.User;

            // Check if user is authenticated
            if (!user.Identity?.IsAuthenticated == true)
            {
                // Redirect to appropriate login page based on the requested area
                var area = context.RouteData.Values["area"]?.ToString();

                if (area?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true)
                {
                    context.Result = new RedirectToActionResult("Login", "Auth", new { area = "Admin" });
                }
                else
                {
                    context.Result = new RedirectToActionResult("Login", "Auth", new { area = "Customer" });
                }
                return;
            }

            // Check if user has required role
            if (_roles.Length > 0 && !_roles.Any(role => user.IsInRole(role)))
            {
                // User is authenticated but doesn't have required role
                var area = context.RouteData.Values["area"]?.ToString();

                if (!string.IsNullOrEmpty(area))
                {
                    // Return area-specific access denied view
                    context.Result = new ViewResult
                    {
                        ViewName = "AccessDenied"
                    };
                }
                else
                {
                    // Return global access denied view
                    context.Result = new ViewResult
                    {
                        ViewName = "~/Views/Shared/AccessDenied.cshtml"
                    };
                }
                return;
            }

            // Check if user status is active
            var statusClaim = user.FindFirst("Status");
            if (statusClaim?.Value != UserStatus.Active)
            {
                // User account is not active
                var area = context.RouteData.Values["area"]?.ToString();

                if (area?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true)
                {
                    context.Result = new RedirectToActionResult("Login", "Auth", new { area = "Admin", message = "Account is not active" });
                }
                else
                {
                    context.Result = new RedirectToActionResult("Login", "Auth", new { area = "Customer", message = "Account is not active" });
                }
                return;
            }
        }
    }

    // Convenience attributes for specific roles
    public class AdminOnlyAttribute : AuthorizeRoleAttribute
    {
        public AdminOnlyAttribute() : base(UserRoles.Admin) { }
    }

    public class CustomerOnlyAttribute : AuthorizeRoleAttribute
    {
        public CustomerOnlyAttribute() : base(UserRoles.Customer) { }
    }

    public class AdminOrCustomerAttribute : AuthorizeRoleAttribute
    {
        public AdminOrCustomerAttribute() : base(UserRoles.Admin, UserRoles.Customer) { }
    }
}
