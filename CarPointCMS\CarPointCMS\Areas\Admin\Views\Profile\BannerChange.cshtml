@{
    ViewData["Title"] = "Edit Banner";
}

@model CarPointCMS.Models.ViewModels.AdminBannerChangeViewModel

<h1 class="h3 mb-3 text-gray-800">Edit Banner</h1>

@if (TempData["success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["success"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@if (TempData["error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["error"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

<div class="row">
    <div class="col-md-8">
        <form asp-area="Admin" asp-controller="Profile" asp-action="BannerChange" method="post" enctype="multipart/form-data">
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="form-group">
                        <label for="">Existing Banner</label>
                        <div>
                            @if (!string.IsNullOrEmpty(ViewBag.AdminData?.Banner))
                            {
                                <img src="~/uploads/admin/@ViewBag.AdminData.Banner" alt="Current Banner" class="w_300" style="max-width: 300px; height: auto;">
                            }
                            else
                            {
                                <img src="~/uploads/admin/default_banner.jpg" alt="Default Banner" class="w_300" style="max-width: 300px; height: auto;">
                            }
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="Banner">Change Banner *</label>
                        <input type="file" asp-for="Banner" class="form-control-file" accept="image/*" required>
                        <span asp-validation-for="Banner" class="text-danger"></span>
                        <small class="form-text text-muted">Allowed formats: JPG, PNG, GIF. Max size: 5MB. Recommended size: 1200x400px</small>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Update Banner
                    </button>
                    <a href="@Url.Action("Change", "Profile", new { area = "Admin" })" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Profile
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
