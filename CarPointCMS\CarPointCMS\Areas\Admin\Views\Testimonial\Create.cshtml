@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Add Testimonial";
}

@model TestimonialCreateViewModel

<h1 class="h3 mb-3 text-gray-800">Add Testimonial</h1>

<form asp-area="Admin" asp-controller="Testimonial" asp-action="Create" method="post" enctype="multipart/form-data">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="Testimonial" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="Name">Name *</label>
                <input asp-for="Name" class="form-control" autofocus>
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Designation">Designation</label>
                <input asp-for="Designation" class="form-control">
                <span asp-validation-for="Designation" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Comment">Content *</label>
                <textarea asp-for="Comment" class="form-control h_100" cols="30" rows="10"></textarea>
                <span asp-validation-for="Comment" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Photo">Photo</label>
                <div>
                    <input asp-for="Photo" type="file">
                    <span asp-validation-for="Photo" class="text-danger"></span>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-success">Submit</button>
    </div>
</form>
