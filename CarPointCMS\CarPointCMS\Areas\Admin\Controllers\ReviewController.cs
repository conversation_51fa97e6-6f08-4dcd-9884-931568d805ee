using Microsoft.AspNetCore.Mvc;
using CarPointCMS.Data;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Models.Entities;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using CarPointCMS.Attributes;
using CarPointCMS.Common;
using Microsoft.EntityFrameworkCore;

namespace CarPointCMS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminOnly]
    public class ReviewController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public ReviewController(ApplicationDbContext context, IAuthenticationService authService)
            : base(authService)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<IActionResult> AdminReview()
        {
            var admin = await GetCurrentAdminAsync();
            if (admin == null)
                return RedirectToAdminLogin();

            // Get all active listings
            var allListings = await _context.Listings
                .Where(l => l.ListingStatus == ListingStatus.Active)
                .OrderBy(l => l.ListingName)
                .ToListAsync();

            // Get admin's own listings
            var adminListings = await _context.Listings
                .Where(l => l.AdminId == admin.Id && l.UserId == null)
                .OrderBy(l => l.ListingName)
                .ToListAsync();

            // Get admin's reviews
            var adminReviews = await _context.Reviews
                .Include(r => r.Listing)
                .Where(r => r.AgentId == admin.Id && r.AgentType == "Admin")
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();

            var model = new AdminReviewViewModel
            {
                Admin = admin,
                AllListings = allListings,
                AdminListings = adminListings,
                AdminReviews = adminReviews
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> CreateAdminReview(AdminReviewCreateViewModel model)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            if (!ModelState.IsValid)
            {
                TempData["error"] = "Please provide a valid review.";
                return RedirectToAction("AdminReview", "Review", new { area = "Admin" });
            }

            try
            {
                var admin = await GetCurrentAdminAsync();
                if (admin == null)
                    return RedirectToLogin();

                // Check if listing exists
                var listing = await _context.Listings.FindAsync(model.ListingId);
                if (listing == null)
                {
                    TempData["error"] = "Listing not found.";
                    return RedirectToAction("AdminReview");
                }

                // Check if admin already reviewed this listing
                var existingReview = await _context.Reviews
                    .FirstOrDefaultAsync(r => r.ListingId == model.ListingId &&
                                            r.AgentId == admin.Id &&
                                            r.AgentType == "Admin");

                if (existingReview != null)
                {
                    TempData["error"] = "You have already reviewed this listing.";
                    return RedirectToAction("AdminReview");
                }

                var review = new Review
                {
                    ListingId = model.ListingId,
                    AgentId = admin.Id,
                    AgentType = "Admin",
                    Rating = model.Rating,
                    ReviewText = model.Review,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Reviews.Add(review);
                await _context.SaveChangesAsync();

                TempData["success"] = "Review has been added successfully.";
                return RedirectToAction("AdminReview");
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while adding the review.";
                return RedirectToAction("AdminReview");
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdateAdminReview(AdminReviewEditViewModel model)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            if (!ModelState.IsValid)
            {
                TempData["error"] = "Please provide a valid review.";
                return RedirectToAction("AdminReview");
            }

            try
            {
                var admin = await GetCurrentAdminAsync();
                if (admin == null)
                    return RedirectToLogin();

                var review = await _context.Reviews
                    .FirstOrDefaultAsync(r => r.Id == model.Id &&
                                            r.AgentId == admin.Id &&
                                            r.AgentType == "Admin");

                if (review == null)
                {
                    TempData["error"] = "Review not found.";
                    return RedirectToAction("AdminReview");
                }

                review.Rating = model.Rating;
                review.ReviewText = model.Review;
                review.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                TempData["success"] = "Review has been updated successfully.";
                return RedirectToAction("AdminReview");
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while updating the review.";
                return RedirectToAction("AdminReview");
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteAdminReview(int id)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            try
            {
                var admin = await GetCurrentAdminAsync();
                if (admin == null)
                    return RedirectToLogin();

                var review = await _context.Reviews
                    .FirstOrDefaultAsync(r => r.Id == id &&
                                            r.AgentId == admin.Id &&
                                            r.AgentType == "Admin");

                if (review == null)
                {
                    TempData["error"] = "Review not found.";
                    return RedirectToAction("AdminReview");
                }

                _context.Reviews.Remove(review);
                await _context.SaveChangesAsync();

                TempData["success"] = "Review has been deleted successfully.";
                return RedirectToAction("AdminReview");
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while deleting the review.";
                return RedirectToAction("AdminReview");
            }
        }

        [HttpGet]
        public async Task<IActionResult> CustomerReview()
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            var customerReviews = await _context.Reviews
                .Include(r => r.Listing)
                .Include(r => r.User)
                .Where(r => r.AgentType == "Customer")
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();

            return View(customerReviews);
        }

        [HttpPost]
        public async Task<IActionResult> DeleteCustomerReview(int id)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            try
            {
                var review = await _context.Reviews
                    .FirstOrDefaultAsync(r => r.Id == id && r.AgentType == "Customer");

                if (review == null)
                {
                    TempData["error"] = "Review not found.";
                    return RedirectToAction("CustomerReview");
                }

                _context.Reviews.Remove(review);
                await _context.SaveChangesAsync();

                TempData["success"] = "Customer review has been deleted successfully.";
                return RedirectToAction("CustomerReview");
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while deleting the review.";
                return RedirectToAction("CustomerReview");
            }
        }

        [HttpGet]
        public async Task<IActionResult> AllReviews()
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            var allReviews = await _context.Reviews
                .Include(r => r.Listing)
                .Include(r => r.User)
                .Include(r => r.Admin)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();

            return View(allReviews);
        }

        [HttpPost]
        public async Task<IActionResult> BulkDeleteReviews(int[] reviewIds)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            if (reviewIds == null || reviewIds.Length == 0)
            {
                TempData["error"] = "No reviews selected.";
                return RedirectToAction("AllReviews");
            }

            try
            {
                var reviews = await _context.Reviews
                    .Where(r => reviewIds.Contains(r.Id))
                    .ToListAsync();

                _context.Reviews.RemoveRange(reviews);
                await _context.SaveChangesAsync();

                TempData["success"] = $"{reviews.Count} review(s) have been deleted successfully.";
                return RedirectToAction("AllReviews");
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while deleting reviews.";
                return RedirectToAction("AllReviews");
            }
        }
    }
}
