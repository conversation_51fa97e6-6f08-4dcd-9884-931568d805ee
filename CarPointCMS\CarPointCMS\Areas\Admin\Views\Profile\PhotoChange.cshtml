@{
    ViewData["Title"] = "Edit Photo";
}

@model CarPointCMS.Models.ViewModels.AdminPhotoChangeViewModel

<h1 class="h3 mb-3 text-gray-800">Edit Photo</h1>

@if (TempData["success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["success"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@if (TempData["error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["error"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

<div class="row">
    <div class="col-md-6">
        <form asp-area="Admin" asp-controller="Profile" asp-action="PhotoChange" method="post" enctype="multipart/form-data">
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="form-group">
                        <label for="">Existing Photo</label>
                        <div>
                            @if (!string.IsNullOrEmpty(ViewBag.CurrentUser?.Photo))
                            {
                                <img src="~/uploads/admin/@ViewBag.CurrentUser.Photo" alt="Current Photo" class="w_150" style="max-width: 150px; height: auto;">
                            }
                            else
                            {
                                <img src="~/uploads/admin/default.jpg" alt="Default Photo" class="w_150" style="max-width: 150px; height: auto;">
                            }
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="Photo">Change Photo *</label>
                        <input type="file" asp-for="Photo" class="form-control-file" accept="image/*" required>
                        <span asp-validation-for="Photo" class="text-danger"></span>
                        <small class="form-text text-muted">Allowed formats: JPG, PNG, GIF. Max size: 2MB</small>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Update Photo
                    </button>
                    <a href="@Url.Action("Change", "Profile", new { area = "Admin" })" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Profile
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
