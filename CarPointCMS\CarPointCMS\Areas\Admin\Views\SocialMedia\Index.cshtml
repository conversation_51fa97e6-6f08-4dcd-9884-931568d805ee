@using CarPointCMS.Models.Entities
@{
    ViewData["Title"] = "Social Media Items";
}

@model IEnumerable<SocialMediaItem>

<h1 class="h3 mb-3 text-gray-800">Social Media Items</h1>

<div class="card shadow mb-4">
    <div class="card-header py-2">
        <h6 class="m-0 font-weight-bold text-primary"></h6>
        <div class="float-right d-inline">
            <a asp-area="Admin" asp-controller="SocialMedia" asp-action="Create" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> Add New</a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                <tr>
                    <th>Serial</th>
                    <th>URL</th>
                    <th>Icon</th>
                    <th>Order</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                    @if (Model != null)
                    {
                        int i = 1;
                        @foreach (var row in Model)
                        {
                            <tr>
                                <td>@i</td>
                                <td>@row.SocialUrl</td>
                                <td><i class="@row.SocialIcon"></i></td>
                                <td>@row.SocialOrder</td>
                                <td>
                                    <a asp-area="Admin" asp-controller="SocialMedia" asp-action="Edit" asp-route-id="@row.Id" class="btn btn-warning btn-sm"><i class="fas fa-edit"></i></a>
                                    <a asp-area="Admin" asp-controller="SocialMedia" asp-action="Delete" asp-route-id="@row.Id" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?');"><i class="fas fa-trash-alt"></i></a>
                                </td>
                            </tr>
                            i++;
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
