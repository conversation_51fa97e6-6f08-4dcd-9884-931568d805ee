@using CarPointCMS.Models.Entities
@{
    ViewData["Title"] = "Pending Comments";
}

@model IEnumerable<Comment>

<h1 class="h3 mb-3 text-gray-800">Pending Comments</h1>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                <tr>
                    <th>Serial</th>
                    <th>Name and Email</th>
                    <th>Comment</th>
                    <th>Blog Title</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                @if (Model != null)
                {
                    int i = 1;
                    @foreach (var row in Model)
                    {
                        <tr>
                            <td>@i</td>
                            <td>@row.PersonName <br>@row.PersonEmail</td>
                            <td>@row.CommentText</td>
                            <td>
                                @row.Blog.PostTitle<br>
                                <a href="@Url.Action("Post", "Blog", new { area = "", slug = row.Blog.PostSlug })" class="btn btn-primary btn-sm" target="_blank">See Blog</a>
                            </td>
                            <td>
                                <a asp-area="Admin" asp-controller="Comment" asp-action="MakeApproved" asp-route-id="@row.Id" class="btn btn-warning btn-sm btn-block" onclick="return confirm('Are you sure?');">Make Approved</a>
                                <a asp-area="Admin" asp-controller="Comment" asp-action="Delete" asp-route-id="@row.Id" class="btn btn-danger btn-sm btn-block" onclick="return confirm('Are you sure?');">Delete</a>
                            </td>
                        </tr>
                        i++;
                    }
                }
                </tbody>
            </table>
        </div>
    </div>
</div>
