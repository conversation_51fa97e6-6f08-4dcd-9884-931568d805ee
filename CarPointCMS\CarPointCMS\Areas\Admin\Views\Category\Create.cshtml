@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Add Category";
}

@model CategoryCreateViewModel

<h1 class="h3 mb-3 text-gray-800">Add Category</h1>

<form asp-area="Admin" asp-controller="Category" asp-action="Create" method="post">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="Category" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="CategoryName">Name *</label>
                <input asp-for="CategoryName" class="form-control" autofocus>
                <span asp-validation-for="CategoryName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CategorySlug">Slug</label>
                <input asp-for="CategorySlug" class="form-control">
                <span asp-validation-for="CategorySlug" class="text-danger"></span>
            </div>
        </div>
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="SeoTitle">Title</label>
                <input asp-for="SeoTitle" class="form-control">
                <span asp-validation-for="SeoTitle" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="SeoMetaDescription">Meta Description</label>
                <textarea asp-for="SeoMetaDescription" class="form-control h_100" cols="30" rows="10"></textarea>
                <span asp-validation-for="SeoMetaDescription" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-success">Submit</button>
        </div>
    </div>
</form>
