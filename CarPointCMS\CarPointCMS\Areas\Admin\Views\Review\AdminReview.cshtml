@using CarPointCMS.Models.ViewModels
@using CarPointCMS.Models.Entities
@{
    ViewData["Title"] = "Admin Reviews";
}

@model AdminReviewViewModel

<h1 class="h3 mb-3 text-gray-800">Admin Reviews</h1>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                <tr>
                    <th>Serial</th>
                    <th>Listing Featured Photo</th>
                    <th>Listing Name</th>
                    <th>My Review</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                    @if (Model?.AllListings != null)
                    {
                        int i = 1;
                        @foreach (var row in Model.AllListings)
                        {
                            @* Skip if this is admin's own listing *@
                            @if (Model.AdminListings.Any(al => al.Id == row.Id))
                            {
                                continue;
                            }
                            <tr>
                                <td>@i</td>
                                <td>
                                    <img src="~/uploads/listing_featured_photos/@row.ListingFeaturedPhoto" alt="" class="w_150">
                                </td>
                                <td>
                                    @row.ListingName <br>
                                    <a href="@Url.Action("Detail", "Listing", new { area = "", id = row.Id, slug = row.ListingSlug })" class="badge badge-success" target="_blank">See Detail</a>
                                </td>
                                <td>
                                    @{
                                        var existingReview = Model.AdminReviews.FirstOrDefault(r => r.ListingId == row.Id);
                                    }
                                    @if (existingReview != null)
                                    {
                                        <div class="my-review">
                                            @for (int star = 1; star <= 5; star++)
                                            {
                                                @if (star <= existingReview.Rating)
                                                {
                                                    <i class="fas fa-star"></i>
                                                }
                                                else
                                                {
                                                    <i class="far fa-star"></i>
                                                }
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <a href="javascript:void(0)" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#add_review_modal@(i)"><i class="fa fa-plus"></i> Add Review</a>
                                    }
                                </td>
                                <td>
                                    @{
                                        var existingReviewForAction = Model.AdminReviews.FirstOrDefault(r => r.ListingId == row.Id);
                                    }
                                    @if (existingReviewForAction != null)
                                    {
                                        <a href="javascript:void(0)" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#update_review_modal@(i)"><i class="fas fa-edit"></i></a>
                                        <a asp-area="Admin" asp-controller="Review" asp-action="DeleteAdminReview" asp-route-id="@existingReviewForAction.Id" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?');"><i class="fas fa-trash-alt"></i></a>
                                    }
                                </td>
                            </tr>

                            <!-- Add Review Modal -->
                            <div class="modal fade" id="add_review_modal@(i)" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Add Review</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <form asp-area="Admin" asp-controller="Review" asp-action="StoreAdminReview" method="post">
                                                <input type="hidden" name="ListingId" value="@row.Id">
                                                <div class="form-group">
                                                    <label>Selected Item</label>
                                                    <div>@row.ListingName</div>
                                                </div>
                                                <div class="form-group">
                                                    <label>Rating</label>
                                                    <select name="Rating" class="form-control">
                                                        @for (int j = 1; j <= 5; j++)
                                                        {
                                                            <option value="@j">@j</option>
                                                        }
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label>Review</label>
                                                    <textarea name="Review" class="form-control h_100" cols="30" rows="10"></textarea>
                                                </div>
                                                <div class="form-group">
                                                    <button type="submit" class="btn btn-success">Submit</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Update Review Modal -->
                            @{
                                var existingReviewForModal = Model.AdminReviews.FirstOrDefault(r => r.ListingId == row.Id);
                            }
                            @if (existingReviewForModal != null)
                            {
                                <div class="modal fade" id="update_review_modal@(i)" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Update Review</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form asp-area="Admin" asp-controller="Review" asp-action="UpdateAdminReview" asp-route-id="@existingReviewForModal.Id" method="post">
                                                    <input type="hidden" name="ListingId" value="@row.Id">
                                                    <div class="form-group">
                                                        <label>Selected Item</label>
                                                        <div>@row.ListingName</div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>Rating</label>
                                                        <select name="Rating" class="form-control">
                                                            @for (int j = 1; j <= 5; j++)
                                                            {
                                                                <option value="@j" selected="@(j == existingReviewForModal.Rating)">@j</option>
                                                            }
                                                        </select>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>Review</label>
                                                        <textarea name="Review" class="form-control h_100" cols="30" rows="10">@existingReviewForModal.ReviewText</textarea>
                                                    </div>
                                                    <div class="form-group">
                                                        <button type="submit" class="btn btn-success">Update</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            i++;
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
