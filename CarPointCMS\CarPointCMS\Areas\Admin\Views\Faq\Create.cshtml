@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Add FAQ";
}

@model FaqCreateViewModel

<h1 class="h3 mb-3 text-gray-800">Add FAQ</h1>

<form asp-area="Admin" asp-controller="Faq" asp-action="Create" method="post">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="Faq" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label asp-for="FaqQuestion">Question *</label>
                        <input asp-for="FaqQuestion" class="form-control" autofocus>
                        <span asp-validation-for="FaqQuestion" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="FaqAnswer">Answer *</label>
                        <textarea asp-for="FaqAnswer" class="form-control editor" cols="30" rows="10"></textarea>
                        <span asp-validation-for="FaqAnswer" class="text-danger"></span>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success">Submit</button>
        </div>
    </div>
</form>
