@using CarPointCMS.Models.Entities
@model GeneralSetting
@{
    ViewData["Title"] = "Edit General Setting";
}

<h1 class="h3 mb-3 text-gray-800">Edit General Setting</h1>

<form asp-area="Admin" asp-controller="Settings" asp-action="General" method="post" enctype="multipart/form-data">
    <input type="hidden" name="CurrentLogo" value="@Model.Logo">
    <input type="hidden" name="CurrentFavicon" value="@Model.Favicon">
    @Html.AntiForgeryToken()

    <div class="card shadow mb-4 t-left">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 col-sm-12">
                    <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <a class="nav-link active" id="p1_tab" data-toggle="pill" href="#p1" role="tab" aria-controls="p1" aria-selected="true">Logo</a>
                        <a class="nav-link" id="p2_tab" data-toggle="pill" href="#p2" role="tab" aria-controls="p2" aria-selected="false">Favicon</a>
                        <a class="nav-link" id="p11_tab" data-toggle="pill" href="#p11" role="tab" aria-controls="p11" aria-selected="false">Top</a>
                        <a class="nav-link" id="p3_tab" data-toggle="pill" href="#p3" role="tab" aria-controls="p3" aria-selected="false">Footer</a>
                        <a class="nav-link" id="p4_tab" data-toggle="pill" href="#p4" role="tab" aria-controls="p4" aria-selected="false">Google Recaptcha</a>
                        <a class="nav-link" id="p5_tab" data-toggle="pill" href="#p5" role="tab" aria-controls="p5" aria-selected="false">Google Analytics</a>
                        <a class="nav-link" id="p6_tab" data-toggle="pill" href="#p6" role="tab" aria-controls="p6" aria-selected="false">Tawk Live Chat</a>
                        <a class="nav-link" id="p7_tab" data-toggle="pill" href="#p7" role="tab" aria-controls="p7" aria-selected="false">Cookie Consent</a>
                        <a class="nav-link" id="p8_tab" data-toggle="pill" href="#p8" role="tab" aria-controls="p8" aria-selected="false">Theme Color</a>
                        <a class="nav-link" id="p9_tab" data-toggle="pill" href="#p9" role="tab" aria-controls="p9" aria-selected="false">Customer Listing Option</a>
                        <a class="nav-link" id="p10_tab" data-toggle="pill" href="#p10" role="tab" aria-controls="p10" aria-selected="false">Layout Direction</a>
                    </div>
                </div>
                <div class="col-md-9 col-sm-12">
                    <div class="tab-content" id="v-pills-tabContent">

                        <div class="tab-pane fade show active" id="p1" role="tabpanel" aria-labelledby="p1_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Existing Logo</label>
                                <div>
                                    @if (!string.IsNullOrEmpty(Model.Logo))
                                    {
                                        <img src="~/uploads/site_photos/@Model.Logo" alt="" class="h_80">
                                    }
                                    else
                                    {
                                        <p class="text-muted">No logo uploaded</p>
                                    }
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Change Logo</label>
                                <div>
                                    <input type="file" name="Logo" accept="image/*">
                                </div>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p2" role="tabpanel" aria-labelledby="p2_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Existing Favicon</label>
                                <div>
                                    @if (!string.IsNullOrEmpty(Model.Favicon))
                                    {
                                        <img src="~/uploads/site_photos/@Model.Favicon" alt="" class="h_80">
                                    }
                                    else
                                    {
                                        <p class="text-muted">No favicon uploaded</p>
                                    }
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Change Favicon</label>
                                <div>
                                    <input type="file" name="Favicon" accept="image/*">
                                </div>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p11" role="tabpanel" aria-labelledby="p11_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Phone</label>
                                <input type="text" name="TopPhone" class="form-control" value="@Model.TopPhone">
                            </div>
                            <div class="form-group">
                                <label for="">Email</label>
                                <input type="text" name="TopEmail" class="form-control" value="@Model.TopEmail">
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p3" role="tabpanel" aria-labelledby="p3_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Footer Address</label>
                                <textarea name="FooterAddress" class="form-control h_70" cols="30" rows="10">@Model.FooterAddress</textarea>
                            </div>
                            <div class="form-group">
                                <label for="">Footer Email</label>
                                <textarea name="FooterEmail" class="form-control h_70" cols="30" rows="10">@Model.FooterEmail</textarea>
                            </div>
                            <div class="form-group">
                                <label for="">Footer Phone</label>
                                <textarea name="FooterPhone" class="form-control h_70" cols="30" rows="10">@Model.FooterPhone</textarea>
                            </div>
                            <div class="form-group">
                                <label for="">Footer Copyright</label>
                                <textarea name="FooterCopyright" class="form-control h_70" cols="30" rows="10">@Model.FooterCopyright</textarea>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p4" role="tabpanel" aria-labelledby="p4_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Google Recaptcha Site Key</label>
                                <input type="text" name="GoogleRecaptchaSiteKey" class="form-control" value="@Model.GoogleRecaptchaSiteKey">
                            </div>
                            <div class="form-group">
                                <label for="">Google Recaptcha Status</label>
                                <select name="GoogleRecaptchaStatus" class="form-control">
                                    <option value="true" @(Model.GoogleRecaptchaStatus ? "selected" : "")>Show</option>
                                    <option value="false" @(!Model.GoogleRecaptchaStatus ? "selected" : "")>Hide</option>
                                </select>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p5" role="tabpanel" aria-labelledby="p5_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Google Analytics Tracking ID</label>
                                <input type="text" name="GoogleAnalyticTrackingId" class="form-control" value="@Model.GoogleAnalyticTrackingId">
                            </div>
                            <div class="form-group">
                                <label for="">Google Analytics Status</label>
                                <select name="GoogleAnalyticStatus" class="form-control">
                                    <option value="Show" @(Model.GoogleAnalyticStatus == "Show" ? "selected" : "")>Show</option>
                                    <option value="Hide" @(Model.GoogleAnalyticStatus == "Hide" ? "selected" : "")>Hide</option>
                                </select>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p6" role="tabpanel" aria-labelledby="p6_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Tawk Live Chat Property ID</label>
                                <input type="text" name="TawkLiveChatPropertyId" class="form-control" value="@Model.TawkLiveChatPropertyId">
                            </div>
                            <div class="form-group">
                                <label for="">Tawk Live Chat Status</label>
                                <select name="TawkLiveChatStatus" class="form-control">
                                    <option value="Show" @(Model.TawkLiveChatStatus == "Show" ? "selected" : "")>Show</option>
                                    <option value="Hide" @(Model.TawkLiveChatStatus == "Hide" ? "selected" : "")>Hide</option>
                                </select>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p7" role="tabpanel" aria-labelledby="p7_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Cookie Consent Message</label>
                                <textarea name="CookieConsentMessage" class="form-control h_70" cols="30" rows="10">@Model.CookieConsentMessage</textarea>
                            </div>
                            <div class="form-group">
                                <label for="">Cookie Consent Button Text</label>
                                <input type="text" name="CookieConsentButtonText" class="form-control" value="@Model.CookieConsentButtonText">
                            </div>
                            <div class="form-group">
                                <label for="">Cookie Consent Text Color</label>
                                <input type="text" name="CookieConsentTextColor" class="form-control jscolor" value="@Model.CookieConsentTextColor">
                            </div>
                            <div class="form-group">
                                <label for="">Cookie Consent Background Color</label>
                                <input type="text" name="CookieConsentBgColor" class="form-control jscolor" value="@Model.CookieConsentBgColor">
                            </div>
                            <div class="form-group">
                                <label for="">Cookie Consent Button Text Color</label>
                                <input type="text" name="CookieConsentButtonTextColor" class="form-control jscolor" value="@Model.CookieConsentButtonTextColor">
                            </div>
                            <div class="form-group">
                                <label for="">Cookie Consent Button Background Color</label>
                                <input type="text" name="CookieConsentButtonBgColor" class="form-control jscolor" value="@Model.CookieConsentButtonBgColor">
                            </div>
                            <div class="form-group">
                                <label for="">Cookie Consent Status</label>
                                <select name="CookieConsentStatus" class="form-control">
                                    <option value="Show" @(Model.CookieConsentStatus == "Show" ? "selected" : "")>Show</option>
                                    <option value="Hide" @(Model.CookieConsentStatus == "Hide" ? "selected" : "")>Hide</option>
                                </select>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p8" role="tabpanel" aria-labelledby="p8_tab">
                            <!-- Tab Content -->
                            <div class="form-group">
                                <label for="">Theme Color</label>
                                <input type="text" name="ThemeColor" class="form-control jscolor" value="@Model.ThemeColor">
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p9" role="tabpanel" aria-labelledby="p9_tab">
                            <!-- Tab Content -->
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="">Customer Listing</label>
                                        <select name="CustomerListingOption" class="form-control">
                                            <option value="On" @(Model.CustomerListingOption == "On" ? "selected" : "")>On</option>
                                            <option value="Off" @(Model.CustomerListingOption == "Off" ? "selected" : "")>Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                        <div class="tab-pane fade" id="p10" role="tabpanel" aria-labelledby="p10_tab">
                            <!-- Tab Content -->
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="">Layout Direction</label>
                                        <select name="LayoutDirection" class="form-control">
                                            <option value="ltr" @(Model.LayoutDirection == "ltr" ? "selected" : "")>LTR</option>
                                            <option value="rtl" @(Model.LayoutDirection == "rtl" ? "selected" : "")>RTL</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- // Tab Content -->
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <button type="submit" class="btn btn-success btn-block mb_50">Update</button>

</form>
