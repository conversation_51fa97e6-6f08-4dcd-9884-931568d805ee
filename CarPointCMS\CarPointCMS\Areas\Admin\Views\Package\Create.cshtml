@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Add Package";
}

@model CreatePackageViewModel

<h1 class="h3 mb-3 text-gray-800">Add Package</h1>

<form asp-area="Admin" asp-controller="Package" asp-action="Create" method="post">
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
                    <div class="float-right d-inline">
                        <a asp-area="Admin" asp-controller="Package" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="PackageType">Type *</label>
                                <select asp-for="PackageType" class="form-control" id="package_type_change">
                                    <option value="">Select Package Type</option>
                                    <option value="Free">Free</option>
                                    <option value="Paid">Paid</option>
                                </select>
                                <span asp-validation-for="PackageType" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="PackageName">Name *</label>
                                <input asp-for="PackageName" class="form-control">
                                <span asp-validation-for="PackageName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="PackagePrice">Price *</label>
                                <input asp-for="PackagePrice" class="form-control" id="package_price">
                                <span asp-validation-for="PackagePrice" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ValidDays">Valid Number of Days *</label>
                                <input asp-for="ValidDays" class="form-control">
                                <span asp-validation-for="ValidDays" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="TotalListings">Allowed Listings *</label>
                                <input asp-for="TotalListings" class="form-control">
                                <span asp-validation-for="TotalListings" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="TotalAmenities">Allowed Amenities Per Listing *</label>
                                <input asp-for="TotalAmenities" class="form-control">
                                <span asp-validation-for="TotalAmenities" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="TotalPhotos">Allowed Photos Per Listing *</label>
                                <input asp-for="TotalPhotos" class="form-control">
                                <span asp-validation-for="TotalPhotos" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="TotalVideos">Allowed Videos Per Listing *</label>
                                <input asp-for="TotalVideos" class="form-control">
                                <span asp-validation-for="TotalVideos" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="TotalSocialItems">Allowed Social Items Per Listing *</label>
                                <input asp-for="TotalSocialItems" class="form-control">
                                <span asp-validation-for="TotalSocialItems" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="TotalAdditionalFeatures">Allowed Additional Features Per Listing *</label>
                                <input asp-for="TotalAdditionalFeatures" class="form-control">
                                <span asp-validation-for="TotalAdditionalFeatures" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="AllowFeatured">Allow Featured Listing *</label>
                                <select asp-for="AllowFeatured" class="form-control">
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                </select>
                                <span asp-validation-for="AllowFeatured" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="PackageOrder">Order</label>
                                <input asp-for="PackageOrder" class="form-control">
                                <span asp-validation-for="PackageOrder" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success">Submit</button>
                </div>
            </div>
        </div>
    </div>
</form>

@section Scripts {
    <script>
        $('#package_type_change').on('change',function() {
            var pt_val = $('#package_type_change').val();
            if(pt_val == 'Free')
            {
                $('#package_price').val(0);
                $('#package_price').prop('readonly', true);
            }
            else
            {
                $('#package_price').val('');
                $('#package_price').prop('readonly', false);
            }
        });
    </script>
}
